<?php

declare(strict_types=1);

namespace App\Filament\Resources\ShsTrackResource\RelationManagers;

use App\Models\ShsStrand;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;

 // Import ShsStrand

final class StrandsRelationManager extends RelationManager
{
    protected static string $relationship = 'strands';

    protected static ?string $recordTitleAttribute = 'strand_name';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('strand_name')
                    ->required()
                    ->maxLength(255)
                    ->label('Strand Name'),
                Forms\Components\Textarea::make('description')
                    ->maxLength(65535)
                    ->columnSpanFull()
                    ->label('Description'),
                // track_id is automatically filled by the relationship
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            // ->recordTitleAttribute('strand_name') // Alternative to static property
            ->columns([
                Tables\Columns\TextColumn::make('id')
                    ->sortable()
                    ->searchable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('strand_name')
                    ->searchable()
                    ->sortable()
                    ->label('Strand Name'),
                Tables\Columns\TextColumn::make('description')
                    ->limit(50)
                    ->tooltip(fn (ShsStrand $record): ?string => $record->description)
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('students_count')->counts('students')
                    ->label('No. of Students')
                    ->sortable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                // Add any relevant filters for strands here
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make(),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(), // Allows viewing the full strand details
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }
}
