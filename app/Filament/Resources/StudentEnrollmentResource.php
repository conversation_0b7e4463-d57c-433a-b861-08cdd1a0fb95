<?php


namespace App\Filament\Resources;

use App\Filament\Exports\StudentEnrollmentExporter;
use App\Filament\Resources\StudentEnrollmentResource\Pages;
use App\Models\Classes;
use App\Models\Course;
use App\Models\Student;
use App\Models\StudentEnrollment;
use App\Models\Subject;
use App\Models\SubjectEnrollment;
use App\Providers\EnrollmentServiceProvider;
// use App\Models\Students; // Removed duplicate/incorrect import
use App\Services\GeneralSettingsService;
use App\Services\StudentService;
// use App\Models\GeneralSettings; // Removed duplicate/incorrect import
use Awcodes\TableRepeater\Components\TableRepeater;
use Awcodes\TableRepeater\Header;
use BezhanSalleh\FilamentShield\Contracts\HasShieldPermissions;
use Exception;
use Filament\Forms;
use Filament\Forms\Components\Actions\Action;
use Filament\Forms\Components\Placeholder;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Tabs;
use Filament\Forms\Form;
use Filament\Forms\Get;
use Filament\Forms\Set;
use Filament\Resources\Components\Tab;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Actions\ExportBulkAction;
use Filament\Tables\Table;
use Guava\FilamentKnowledgeBase\Contracts\HasKnowledgeBase;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Support\Facades\Cache; // Import the new service
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage; // Added Service Provider
use Illuminate\Support\HtmlString; // Import the settings service
use Illuminate\Support\Str;
use Livewire\Component as Livewire;

// Import the correct Cache facade

final class StudentEnrollmentResource extends Resource implements HasKnowledgeBase, HasShieldPermissions
{
    protected static ?string $model = StudentEnrollment::class;

    // protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    protected static ?string $navigationGroup = 'Enrollments';

    // protected static bool $shouldRegisterNavigation = false;
    public static function getPermissionPrefixes(): array
    {
        return [
            'view',
            'view_any',
            'create',
            'update',
            'restore',
            'restore_any',
            'replicate',
            'reorder',
            'delete',
            'delete_any',
            'force_delete',
            'force_delete_any',
        ];
    }

    public static function getDocumentation(): array
    {
        return [
            'enrollment.overview',
            'enrollment.process',
            'tuition.calculation',
            'subjects.overview',
            'troubleshooting.faq',
        ];
    }

    // public static function getNavigationBadge(): ?string
    // {
    //     return static::getModel()::count();
    // }

    public static function form(Form $form): Form
    {
        $generalSettingsService = app(GeneralSettingsService::class); // Use the service
        $generalSettingsService->getGlobalSettingsModel();

        return $form
            ->schema([
                // Select component for Student ID with Create Option
                Select::make('student_id')
                    ->label('Student')
                    ->required()
                    ->relationship(name: 'student', titleAttribute: 'last_name') // Use relationship
                    ->getOptionLabelFromRecordUsing(fn (Student $record): string => "{$record->id} - {$record->last_name}, {$record->first_name}") // Custom label
                    ->searchable(['id', 'first_name', 'last_name']) // Search multiple columns
                    ->preload()
                    ->live() // Keep live for dependent fields
                    ->createOptionForm([ // Modal form for new student
                        // Placeholder to preview the next available ID
                        Placeholder::make('preview_student_id')
                            ->label('Generated Student ID')
                            ->content(function (): HtmlString {
                                $nextId = Student::generateNextId();

                                return new HtmlString("<span class='text-lg font-semibold text-primary-600 dark:text-primary-400'>{$nextId}</span> (This ID will be assigned upon creation)");
                            }),
                        Forms\Components\TextInput::make('first_name')
                            ->required()
                            ->maxLength(255),
                        Forms\Components\TextInput::make('last_name')
                            ->required()
                            ->maxLength(255),
                        Forms\Components\TextInput::make('middle_name')
                            ->maxLength(255),
                        Forms\Components\TextInput::make('email')
                            ->email()
                            ->required()
                            ->maxLength(255)
                            ->unique(table: Student::class, column: 'email', ignoreRecord: true), // Unique email check
                        Select::make('course_id')
                            ->label('Course')
                            ->options(Course::pluck('code', 'id'))
                            ->searchable()
                            ->required()
                            ->live()
                            ->afterStateUpdated(function (Get $get, Set $set, $state): void {
                                // Update miscellaneous fee when course changes
                                if ($state) {
                                    $course = Course::find($state);
                                    $miscellaneousFee = $course?->getMiscellaneousFee() ?? 3500;
                                    $set('miscellaneous', $miscellaneousFee);

                                    // Recalculate totals
                                    $totalTuition = (float) ($get('Total_Tuition') ?? 0);
                                    $overallTotal = $totalTuition + $miscellaneousFee;
                                    $set('overall_total', $overallTotal);

                                    $downpayment = (float) ($get('downpayment') ?? 0);
                                    $balance = $overallTotal - $downpayment;
                                    $set('total_balance', $balance);
                                } else {
                                    // Reset to default if no course selected
                                    $set('miscellaneous', 3500);
                                }
                            }),
                        Select::make('academic_year')
                            ->label('Starting Academic Year')
                            ->options([
                                '1' => '1st Year',
                                '2' => '2nd Year',
                                '3' => '3rd Year',
                                '4' => '4th Year',
                            ])
                            ->required()
                            ->default('1'),
                        Select::make('gender')
                            ->label('Gender')
                            ->options([
                                'Male' => 'Male',
                                'Female' => 'Female',
                            ])
                            ->required(),
                        Forms\Components\DatePicker::make('birth_date')
                            ->label('Birth Date')
                            ->required(),
                    ])
                    ->createOptionAction(fn (Action $action): Action => $action
                        ->modalHeading('Create New Student')
                        ->modalButton('Create Student')
                        ->modalWidth('xl'))
                    ->createOptionUsing(function (array $data, StudentService $studentService): int { // Inject StudentService
                        $student = $studentService->createStudent($data); // Call the service method

                        return $student instanceof Student ? $student->id : 0; // Return ID on success, 0 on failure
                    })
                    ->afterStateUpdated(function (callable $set, $state, Get $get): void { // Update dependent fields after selection/creation
                        if ($state) {
                            $student = Student::find($state);
                            if ($student) { // Check if student exists
                                $set('full_name', $student->full_name);
                                $set('guest_email', $student->email ?? '');
                                $set('course_id', $student->course_id ?? '');
                                $set('academic_year', $student->academic_year ?? '');

                                // Update miscellaneous fee based on student's course
                                if ($student->course_id) {
                                    $course = Course::find($student->course_id);
                                    $miscellaneousFee = $course?->getMiscellaneousFee() ?? 3500;
                                    $set('miscellaneous', $miscellaneousFee);

                                    // Recalculate totals
                                    $totalTuition = (float) ($get('Total_Tuition') ?? 0);
                                    $overallTotal = $totalTuition + $miscellaneousFee;
                                    $set('overall_total', $overallTotal);

                                    $downpayment = (float) ($get('downpayment') ?? 0);
                                    $balance = $overallTotal - $downpayment;
                                    $set('total_balance', $balance);
                                }
                                // Removed picture_1x1 logic as it's not in the main form state
                            } else {
                                // Clear fields if student not found (e.g., after failed creation)
                                $set('full_name', null);
                                $set('guest_email', null);
                                $set('course_id', null);
                                $set('academic_year', null);
                                $set('miscellaneous', 3500); // Reset to default
                            }
                        } else {
                            // Clear fields if student deselected
                            $set('full_name', null);
                            $set('guest_email', null);
                            $set('course_id', null);
                            $set('academic_year', null);
                            $set('miscellaneous', 3500); // Reset to default
                        }
                    })
                    ->afterStateHydrated(function (Get $get, Set $set, $record): void { // Populate on edit
                        if (isset($record->student)) {
                            $set('full_name', $record->student->full_name);
                            $set('guest_email', $record->student->email ?? '');
                            $set('course_id', $record->student->course_id ?? '');
                            $set('academic_year', $record->student->academic_year ?? '');
                            // Removed selected_semester and discount setting here as they belong to enrollment/tuition
                            $set('downpayment', $record->downpayment ?? 0);

                            // Set miscellaneous fee from existing tuition record or course
                            if ($record->studentTuition) {
                                $set('miscellaneous', $record->studentTuition->total_miscelaneous_fees);
                            } elseif ($record->course) {
                                $miscellaneousFee = $record->course->getMiscellaneousFee();
                                $set('miscellaneous', $miscellaneousFee);
                            }
                        }
                    }),

                // Semester and Academic Year for the ENROLLMENT (not the student's starting year)
                Select::make('semester')
                    ->label('Enrollment Semester')
                    ->options($generalSettingsService->getAvailableSemesters())
                    ->default($generalSettingsService->getCurrentSemester())
                    ->required(),
                Select::make('academic_year')
                    ->label('Enrollment Academic Year')
                    ->options([
                        '1' => '1st Year',
                        '2' => '2nd Year',
                        '3' => '3rd Year',
                        '4' => '4th Year',
                    ])
                    ->required()
                    ->live(),

                // Student Info Tab (conditionally visible)
                Tabs::make('Student Info')
                    ->visible(fn (Get $get): bool => $get('student_id') !== null)
                    ->columnSpanFull()
                    ->tabs([
                        Tabs\Tab::make('Student Information')
                            ->columns(2)
                            ->schema([
                                Placeholder::make('clearance_status')
                                    ->label('Clearance Status')
                                    ->content(function (Get $get): HtmlString {
                                        $studentId = $get('student_id');

                                        if (! $studentId) {
                                            return new HtmlString('
                                                <div class="flex items-center space-x-2">
                                                    <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                                    </svg>
                                                    <span class="text-sm text-gray-500 dark:text-gray-400">Select a student first</span>
                                                </div>
                                            ');
                                        }

                                        $student = Student::find($studentId);
                                        if (! $student) {
                                            return new HtmlString('
                                                <div class="flex items-center space-x-2">
                                                    <svg class="w-4 h-4 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                                    </svg>
                                                    <span class="text-sm text-red-600 dark:text-red-400 font-medium">Student not found</span>
                                                </div>
                                            ');
                                        }

                                        $isCleared = $student->hasCurrentClearance();

                                        if ($isCleared) {
                                            return new HtmlString('
                                                <div class="flex items-center space-x-2">
                                                    <div class="flex items-center justify-center w-6 h-6 bg-green-100 dark:bg-green-900/20 rounded-full">
                                                        <svg class="w-4 h-4 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                                        </svg>
                                                    </div>
                                                    <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400 border border-green-200 dark:border-green-800">
                                                        ✓ Cleared
                                                    </span>
                                                </div>
                                            ');
                                        }

                                        return new HtmlString('
                                                <div class="flex items-center space-x-3" x-data="{
                                                    clearing: false,
                                                    async clearStudent() {
                                                        if (this.clearing) return;
                                                        this.clearing = true;

                                                        try {
                                                            await $wire.call(\'clearStudent\', '.$student->id.');
                                                            setTimeout(() => window.location.reload(), 1000);
                                                        } catch (error) {
                                                            console.error(\'Error clearing student:\', error);
                                                        } finally {
                                                            this.clearing = false;
                                                        }
                                                    }
                                                }">
                                                    <div class="flex items-center space-x-2">
                                                        <div class="flex items-center justify-center w-6 h-6 bg-red-100 dark:bg-red-900/20 rounded-full">
                                                            <svg class="w-4 h-4 text-red-600 dark:text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                                            </svg>
                                                        </div>
                                                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400 border border-red-200 dark:border-red-800">
                                                            ✗ Not cleared
                                                        </span>
                                                    </div>

                                                    <button
                                                        type="button"
                                                        @click="clearStudent()"
                                                        :disabled="clearing"
                                                        class="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
                                                    >
                                                        <svg x-show="!clearing" class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                                        </svg>
                                                        <svg x-show="clearing" class="w-3 h-3 mr-1 animate-spin" fill="none" viewBox="0 0 24 24">
                                                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                                        </svg>
                                                        <span x-text="clearing ? \'Clearing...\' : \'Clear Student\'"></span>
                                                    </button>
                                                </div>
                                            ');

                                    }),
                                Forms\Components\TextInput::make('guest_email') // Consider renaming or removing if redundant
                                    ->label('Student Email')
                                    ->email()
                                    ->columnSpan(1)
                                    ->readOnly(),
                                Forms\Components\TextInput::make('full_name') // Consider renaming or removing if redundant
                                    ->label('Student Full Name')
                                    ->columnSpan(2)
                                    ->readOnly(),
                                Select::make('course_id') // This should likely be read-only or removed if set by student selection
                                    ->label('Student Course')
                                    ->options(Course::pluck('code', 'id'))
                                    ->disabled() // Make read-only in this context
                                    ->searchable()
                                    ->columnSpan(1),
                            ]),

                    ]),

                // Table Repeater for Subjects
                TableRepeater::make('subjectsEnrolled')
                    ->headers([
                        Header::make('subject code')->width('150px'),
                        Header::make('modular')->label('Modular')->width('100px'),
                        Header::make('subject title')->width('150px'),
                        Header::make('subject lectures')->width('150px'),
                        Header::make('subject laboratory')->width('150px'),
                        Header::make('section')->width('100px'),
                    ])
                    ->relationship('subjectsEnrolled')
                    ->mutateRelationshipDataBeforeCreateUsing(function (array $data, Livewire $livewire) {
                        $data['academic_year'] = $livewire->data['academic_year'];
                        $data['school_year'] = self::getCurrentSchoolYear();
                        $data['semester'] = self::getCurrentSemester();
                        $data['student_id'] = $livewire->data['student_id'];
                        if (empty($data['lecture_fee']) || empty($data['laboratory_fee'])) {
                            $subject = Subject::with('course')->find($data['subject_id']);
                            if ($subject) {
                                $isNSTP = str_contains(mb_strtoupper($subject->code), 'NSTP');
                                $totalUnits = $subject->lecture + $subject->laboratory;
                                $courseLecPerUnit = $subject->course?->lec_per_unit ?? 0;
                                $lectureFee = $subject->lecture ? $totalUnits * $courseLecPerUnit : 0;
                                if ($isNSTP) {
                                    $lectureFee *= 0.5;
                                }
                                $courseLabPerUnit = $subject->course?->lab_per_unit ?? 0;
                                $laboratoryFee = $subject->laboratory ? 1 * $courseLabPerUnit : 0;
                                $data['lecture_fee'] = $data['lecture'] ?? $lectureFee;
                                $data['laboratory_fee'] = $data['laboratory'] ?? $laboratoryFee;
                            }
                        }

                        return $data;
                    })
                    ->mutateRelationshipDataBeforeSaveUsing(function (array $data) {
                        $data['lecture_fee'] = $data['lecture'];
                        $data['laboratory_fee'] = $data['laboratory'];

                        // Make sure section is set as class_id
                        if (! empty($data['section']) && (empty($data['class_id']) || $data['class_id'] !== $data['section'])) {
                            $data['class_id'] = $data['section'];
                        }

                        return $data;
                    })
                    ->schema([
                        Select::make('subject_id')
                            ->label('Subject Code')
                            ->columnSpan(1)
                            ->searchable()
                            ->required()
                            ->options(function (Get $get, Livewire $livewire, $record = null): array {
                                $selectedCourse = $livewire->data['course_id'] ?? null;
                                $studentId = $livewire->data['student_id'] ?? null;

                                // Return empty array if essential data is missing
                                if (empty($selectedCourse) || empty($studentId)) {
                                    return [];
                                }

                                // Get current academic period from GeneralSettingsService
                                $schoolYear = self::getCurrentSchoolYear();
                                $semester = self::getCurrentSemester();

                                // Get already enrolled subjects for this student in current enrollment
                                $enrolledSubjectIds = collect($livewire->data['subjectsEnrolled'] ?? [])
                                    ->pluck('subject_id')
                                    ->filter()
                                    ->values()
                                    ->toArray();

                                // Get ALL subjects for the student's course (not limited to Classes)
                                $allCourseSubjects = Subject::where('course_id', $selectedCourse)
                                    ->with('course')
                                    ->get()
                                    ->reject(fn ($subject): bool => in_array($subject->id, $enrolledSubjectIds));

                                // Get available classes to check which subjects have classes (for star indicator)
                                $availableClasses = Classes::where('school_year', $schoolYear)
                                    ->where('semester', $semester)
                                    ->whereJsonContains('course_codes', (string) $selectedCourse)
                                    ->with(['Subject'])
                                    ->get();

                                $subjectsWithClasses = $availableClasses
                                    ->map(fn ($class) => $class->Subject ? $class->Subject->code : null)
                                    ->filter()
                                    ->unique();

                                // Format for dropdown with stars and subject codes
                                $options = [];
                                foreach ($allCourseSubjects as $subject) {
                                    $hasClasses = $subjectsWithClasses->contains($subject->code);
                                    $star = $hasClasses ? '⭐ ' : '';
                                    $options[$subject->id] = $star.$subject->code;
                                }

                                return $options;
                            })
                            ->searchable()
                            ->live()
                            ->reactive()
                            ->placeholder('Select subject')
                            ->afterStateUpdated(function (Set $set, $state, Get $get, $record, Livewire $livewire): void {
                                $selectedCourse = $livewire->data['course_id'] ?? null;

                                if ($state && $selectedCourse) {
                                    $subject = Subject::with('course')->find($state);

                                    if ($subject) {
                                        $set('title', $subject->title);
                                        $set('subject_code', $subject->code);
                                        $set('pre_riquisite', implode(', ', $subject->pre_riquisite ?? []));

                                        // Calculate fees
                                        $isNSTP = str_contains(mb_strtoupper($subject->code), 'NSTP');
                                        $totalUnits = $subject->lecture + $subject->laboratory;
                                        $courseLecPerUnit = $subject->course?->lec_per_unit ?? 0;
                                        $lectureFee = $subject->lecture ? $totalUnits * $courseLecPerUnit : 0;
                                        if ($isNSTP) {
                                            $lectureFee *= 0.5;
                                        }
                                        $set('lecture', $lectureFee ?: '0');

                                        $courseLabPerUnit = $subject->course?->lab_per_unit ?? 0;
                                        $laboratoryFee = $subject->laboratory ? 1 * $courseLabPerUnit : 0;
                                        $set('laboratory', $laboratoryFee ?: '0');

                                        // Get available sections for this subject and course in the correct academic period
                                        $schoolYear = self::getCurrentSchoolYear();
                                        $semester = self::getCurrentSemester();

                                        $classes = Classes::where('school_year', $schoolYear)
                                            ->where('semester', $semester)
                                            ->whereRaw('LOWER(subject_code) = LOWER(?)', [$subject->code])
                                            ->whereJsonContains('course_codes', (string) $selectedCourse)
                                            ->pluck('section', 'id')
                                            ->toArray();
                                        $set('section_options', $classes);

                                        // Clear section selection when subject changes
                                        $set('section', null);
                                    }
                                } else {
                                    // Clear all fields if no subject selected
                                    $set('title', '');
                                    $set('lecture', 0);
                                    $set('laboratory', 0);
                                    $set('pre_riquisite', '');
                                    $set('subject_code', '');
                                    $set('section_options', []);
                                    $set('section', null);
                                }

                                // Update totals if not editing an existing record
                                if (! $record) {
                                    EnrollmentServiceProvider::updateTotals($get, $set, null);
                                }
                            }),
                        Forms\Components\Toggle::make('is_modular')
                            ->label('Is Modular')
                            ->columnSpan(1)
                            ->reactive()
                            ->afterStateUpdated(function (Get $get, Set $set, $record): void { // Pass $record
                                $subjectId = $get('subject_id');
                                if ($subjectId) {
                                    $subject = Subject::with('course')->find($subjectId);
                                    if ($subject) {
                                        if ($get('is_modular')) {
                                            $set('lecture', 2400);
                                            $set('laboratory', 0);
                                        } else {
                                            $totalUnits = $subject->lecture + $subject->laboratory;
                                            $courseLecPerUnit = $subject->course?->lec_per_unit ?? 0;
                                            $lectureFee = $subject->lecture ? $totalUnits * $courseLecPerUnit : 0;
                                            $isNSTP = str_contains(mb_strtoupper($subject->code), 'NSTP');
                                            if ($isNSTP) {
                                                $lectureFee *= 0.5;
                                            }
                                            $set('lecture', $lectureFee ?: '0');
                                            $courseLabPerUnit = $subject->course?->lab_per_unit ?? 0;
                                            $set('laboratory', $subject->laboratory ? 1 * $courseLabPerUnit : '0');
                                        }
                                    }
                                }
                                EnrollmentServiceProvider::updateTotals($get, $set, $record); // Pass $record
                            }),
                        Forms\Components\TextInput::make('title')->label('Subject Title')->columnSpan(1)->readOnly(),
                        Forms\Components\TextInput::make('lecture')->label('Lecture Fee')->columnSpan(1)->prefix('₱')->live(onBlur: true)->numeric()
                            ->afterStateUpdated(fn (Get $get, Set $set, $record) => EnrollmentServiceProvider::updateTotals($get, $set, $record)),
                        Forms\Components\TextInput::make('laboratory')->label('Laboratory Fee')->columnSpan(1)->prefix('₱')->live(onBlur: true)->numeric()
                            ->afterStateUpdated(fn (Get $get, Set $set, $record) => EnrollmentServiceProvider::updateTotals($get, $set, $record)),
                        Select::make('section')
                            ->options(function (Get $get, Livewire $livewire) {
                                $options = is_array($get('section_options')) ? $get('section_options') : [];

                                // If options are empty, but we have subject_id, try to load them
                                if ($options === [] && ! empty($get('subject_id'))) {
                                    $subject = Subject::find($get('subject_id'));
                                    $selectedCourse = $livewire->data['course_id'] ?? null;

                                    if ($subject && $selectedCourse) {
                                        $schoolYear = self::getCurrentSchoolYear();
                                        $semester = self::getCurrentSemester();

                                        return Classes::where('school_year', $schoolYear)
                                            ->where('semester', $semester)
                                            ->whereRaw('LOWER(subject_code) = LOWER(?)', [$subject->code])
                                            ->whereJsonContains('course_codes', (string) $selectedCourse)
                                            ->pluck('section', 'id')
                                            ->toArray();
                                    }
                                }

                                return $options;
                            })
                            ->label('Section')
                            ->columnSpan(1)
                            ->required()
                            ->reactive()
                            ->afterStateUpdated(function ($state, Get $get, Set $set, Livewire $livewire): void {
                                if ($state) {
                                    $set('class_id', $state);
                                }
                                // Don't call updateTotals directly here with record - it's the wrong type
                                // Instead, just update class_id and let the parent form handle totals
                            })
                            ->afterStateHydrated(function ($state, Get $get, Set $set, $record): void {
                                // Skip if not in repeater/edit context
                                if (! is_array($record)) {
                                    return;
                                }

                                // If we're editing an existing record with class_id but no section options loaded yet
                                if (! empty($record['class_id']) && empty($get('section_options'))) {
                                    // Get the class record
                                    $class = Classes::find($record['class_id']);
                                    if ($class) {
                                        // Get the subject and course to load section options
                                        $subject = Subject::find($record['subject_id']);
                                        if ($subject) {
                                            // Try to get course_id from parent form
                                            $selectedCourse = $get('course_id');
                                            if (empty($selectedCourse) && ! empty($record['course_id'])) {
                                                $selectedCourse = $record['course_id'];
                                            }

                                            if ($selectedCourse) {
                                                $schoolYear = self::getCurrentSchoolYear();
                                                $semester = self::getCurrentSemester();

                                                $classes = Classes::where('school_year', $schoolYear)
                                                    ->where('semester', $semester)
                                                    ->whereRaw('LOWER(subject_code) = LOWER(?)', [$subject->code])
                                                    ->whereJsonContains('course_codes', (string) $selectedCourse)
                                                    ->pluck('section', 'id')
                                                    ->toArray();

                                                if (! empty($classes)) {
                                                    $set('section_options', $classes);
                                                    // Now set the section value
                                                    $set('section', $record['class_id']);
                                                }
                                            }
                                        }
                                    }
                                }

                                // If we have section_options but no value set, check if we have class_id to set
                                if (empty($state) && ! empty($record['class_id']) && ! empty($get('section_options'))) {
                                    $set('section', $record['class_id']);
                                }
                            }),

                        Forms\Components\TextInput::make('pre_riquisite')->label('Pre-requisites')->visible(false)->readOnly(),
                        Forms\Components\Hidden::make('subject_code'),
                        Forms\Components\Hidden::make('class_id'),
                        Forms\Components\Hidden::make('section_options'),
                    ])
                    ->showLabels(false)
                    ->emptyLabel('Enroll a Subject for this Student')
                    ->columnSpanFull()
                    ->streamlined()
                    ->live()
                    ->afterStateHydrated(function (Get $get, Set $set, $record): void { // Pass Set as nullable
                        $subjectsEnrolled = $get('subjectsEnrolled');
                        foreach ($subjectsEnrolled as $index => $subject) {
                            if (! empty($subject['subject_id'])) {
                                $subjectEnrollment = SubjectEnrollment::with(['subject.course', 'class'])->find($subject['id'] ?? null); // Handle potential null ID
                                if ($subjectEnrollment) {
                                    $set('subjectsEnrolled.'.$index.'.title', $subjectEnrollment->subject->title);
                                    $set('subjectsEnrolled.'.$index.'.subject_id', $subjectEnrollment->subject_id); // Ensure subject_id is set
                                    $set('subjectsEnrolled.'.$index.'.subject_code', $subjectEnrollment->subject->code ?? '');

                                    if ($subjectEnrollment->lecture_fee !== null && $subjectEnrollment->laboratory_fee !== null) {
                                        $set('subjectsEnrolled.'.$index.'.lecture', $subjectEnrollment->lecture_fee);
                                        $set('subjectsEnrolled.'.$index.'.laboratory', $subjectEnrollment->laboratory_fee);
                                    } else {
                                        $subjectModel = $subjectEnrollment->subject;
                                        $isNSTP = str_contains(mb_strtoupper((string) $subjectModel->code), 'NSTP');
                                        $totalUnits = $subjectModel->lecture + $subjectModel->laboratory;
                                        $courseLecPerUnit = $subjectModel->course?->lec_per_unit ?? 0;
                                        $lectureFee = $subjectModel->lecture ? $totalUnits * $courseLecPerUnit : 0;
                                        if ($isNSTP) {
                                            $lectureFee *= 0.5;
                                        }
                                        $courseLabPerUnit = $subjectModel->course?->lab_per_unit ?? 0;
                                        $laboratoryFee = $subjectModel->laboratory ? 1 * $courseLabPerUnit : 0;
                                        $set('subjectsEnrolled.'.$index.'.lecture', $lectureFee);
                                        $set('subjectsEnrolled.'.$index.'.laboratory', $laboratoryFee);
                                        $subjectEnrollment->update(['lecture_fee' => $lectureFee, 'laboratory_fee' => $laboratoryFee]);
                                    }
                                    $set('subjectsEnrolled.'.$index.'.is_modular', $subjectEnrollment->is_modular);

                                    // Also set the class_id and section if available
                                    if ($subjectEnrollment->class_id) {
                                        $set('subjectsEnrolled.'.$index.'.class_id', $subjectEnrollment->class_id);

                                        // Load section options and set the selected section
                                        $subject = $subjectEnrollment->subject;
                                        $student = $record->student;
                                        if ($subject && $student) {
                                            $selectedCourse = $student->course_id ?? null;
                                            if ($selectedCourse) {
                                                $schoolYear = self::getCurrentSchoolYear();
                                                $semester = self::getCurrentSemester();

                                                $classes = Classes::where('school_year', $schoolYear)
                                                    ->where('semester', $semester)
                                                    ->whereRaw('LOWER(subject_code) = LOWER(?)', [$subject->code])
                                                    ->whereJsonContains('course_codes', (string) $selectedCourse)
                                                    ->pluck('section', 'id')
                                                    ->toArray();
                                                $set('subjectsEnrolled.'.$index.'.section_options', $classes);
                                                $set('subjectsEnrolled.'.$index.'.section', $subjectEnrollment->class_id);
                                            }
                                        }
                                    }
                                }
                            }
                        }
                        EnrollmentServiceProvider::updateTotals($get, $set, $record);
                    })
                    ->afterStateUpdated(fn (Get $get, Set $set, $record) => EnrollmentServiceProvider::updateTotals($get, $set, $record))
                    ->deleteAction(fn (Action $action): Action => $action->after(fn (Get $get, Set $set, $record) => EnrollmentServiceProvider::updateTotals($get, $set, $record))),

                // Class Schedules Section
                Section::make('Class Schedules')
                    ->columnSpanFull()
                    ->schema([
                        Placeholder::make('class_schedules')
                            ->label('Class Schedules')
                            ->live() // Make it reactive to changes
                            ->content(function (Get $get): HtmlString { // Removed Set $set as it's not used
                                $subjectsEnrolled = $get('subjectsEnrolled');
                                $scheduleData = [];

                                if (empty($subjectsEnrolled)) {
                                    return new HtmlString('<p class="text-sm text-gray-500 dark:text-gray-400">No subjects enrolled yet. Select subjects and sections to see schedules.</p>');
                                }

                                foreach ($subjectsEnrolled as $subject) {
                                    // Check both section and class_id fields
                                    $sectionId = $subject['section'] ?? $subject['class_id'] ?? null;

                                    if (! empty($sectionId)) {
                                        $class = Classes::with('Schedule.room')->find($sectionId);
                                        if ($class && $class->Schedule) {
                                            $subjectCode = $subject['subject_code'] ?? null;
                                            if (empty($subjectCode) && ! empty($subject['subject_id'])) {
                                                $subjectModel = Subject::find($subject['subject_id']);
                                                $subjectCode = $subjectModel ? $subjectModel->code : 'Unknown';
                                            }

                                            foreach ($class->Schedule as $schedule) {
                                                $weekDay = mb_strtolower((string) $schedule->day_of_week);
                                                $startTime = date('g:i A', strtotime((string) $schedule->start_time));
                                                $endTime = date('g:i A', strtotime((string) $schedule->end_time));
                                                $room = $schedule->room->name ?? 'TBA';
                                                $section = $class->section ?? '';
                                                $scheduleData[$subjectCode][$weekDay] = "<b>$room | $section</b> <br> $startTime - $endTime";
                                            }
                                        }
                                    }
                                }

                                // If no schedule data found, show helpful message
                                if ($scheduleData === []) {
                                    return new HtmlString('<p class="text-sm text-gray-500 dark:text-gray-400">No schedules available. Make sure you have selected sections for your subjects.</p>');
                                }

                                // Build HTML table (keep existing logic)
                                $table = '<table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">';
                                $table .= '<thead class="bg-gray-50 dark:bg-gray-800"><tr>';
                                $table .= '<th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Subject</th>';
                                $daysHeader = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
                                foreach ($daysHeader as $day) {
                                    $table .= '<th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">'.$day.'</th>';
                                }
                                $table .= '</tr></thead>';
                                $table .= '<tbody class="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">';
                                foreach ($scheduleData as $subjectCode => $schedule) {
                                    $table .= '<tr><td class="px-6 py-4 whitespace-nowrap">'.$subjectCode.'</td>';
                                    $days = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'];
                                    foreach ($days as $day) {
                                        $content = $schedule[$day] ?? '';
                                        $cellClass = $content !== '' && $content !== '0' ? 'bg-primary-500/50 text-primary-300 border border-primary-500' : '';
                                        $table .= '<td class="px-1 text-center '.$cellClass.'">'.$content.'</td>';
                                    }
                                    $table .= '</tr>';
                                }
                                $table .= '</tbody></table>';

                                return new HtmlString($table);
                            }),
                    ]),

                // Assessment Section
                Section::make('Assessment')
                    ->description('This section is for the assessment of the student')
                    ->columnSpanFull()
                    ->schema([
                        Forms\Components\Hidden::make('is_manually_modified')->default(fn ($record): bool => $record && $record->studentTuition !== null),
                        Select::make('discount')
                            ->label('Discount')
                            ->live()
                            ->options(function () {
                                $options = ['0' => 'No Discount'];
                                for ($i = 5; $i <= 100; $i += 5) {
                                    $options[(string) $i] = $i.'% Discount';
                                }

                                return $options;
                            })
                            ->default(fn ($record): string => $record && $record->studentTuition ? (string) $record->studentTuition->discount : '0')
                            ->afterStateUpdated(function (Get $get, Set $set, $record): void {
                                if (! $get('original_lecture_amount')) {
                                    $set('original_lecture_amount', $get('total_lectures'));
                                }
                                $set('is_manually_modified', true);
                                EnrollmentServiceProvider::recalculateTotals($get, $set, $record);
                            })
                            ->suffix('%'),
                        Forms\Components\Hidden::make('original_lecture_amount'),
                        Forms\Components\Hidden::make('is_overall_manually_modified')
                            ->default(false),
                        Forms\Components\Hidden::make('original_overall_amount'),
                        Forms\Components\TextInput::make('total_lectures')
                            ->numeric()
                            ->prefix('₱')
                            ->live(onBlur: true)
                            ->default(fn ($record) => $record && $record->studentTuition ? $record->studentTuition->total_lectures : null)
                            ->afterStateUpdated(function (Get $get, Set $set, $record): void {
                                $set('is_manually_modified', true);
                                $set('original_lecture_amount', $get('total_lectures'));
                                EnrollmentServiceProvider::recalculateTotals($get, $set, $record);
                            })
                            ->label('Total Lecture'),
                        Forms\Components\TextInput::make('total_laboratory')
                            ->numeric()
                            ->prefix('₱')
                            ->live(onBlur: true)
                            ->default(fn ($record) => $record && $record->studentTuition ? $record->studentTuition->total_laboratory : null)
                            ->afterStateUpdated(function (Get $get, Set $set, $record): void {
                                $set('is_manually_modified', true);
                                EnrollmentServiceProvider::recalculateTotals($get, $set, $record);
                            })
                            ->label('Total Laboratory'),
                        Forms\Components\TextInput::make('Total_Tuition')
                            ->numeric()
                            ->prefix('₱')
                            ->live()
                            ->label('Total Tuition'),
                        Forms\Components\TextInput::make('miscellaneous')
                            ->numeric()
                            ->prefix('₱')
                            ->live(onBlur: true)
                            ->default(function (Get $get, $record) {
                                // For existing records, use the tuition record value
                                if ($record && $record->studentTuition) {
                                    return $record->studentTuition->total_miscelaneous_fees;
                                }

                                // For new records, get from course or default
                                $courseId = $get('course_id');
                                if ($courseId) {
                                    $course = Course::find($courseId);

                                    return $course?->miscelaneous ?? 3500;
                                }

                                return 3500; // Final fallback
                            })
                            ->afterStateUpdated(function (Get $get, Set $set, $record): void {
                                // Recalculate overall total when miscellaneous changes
                                $totalTuition = (float) ($get('Total_Tuition') ?? 0);
                                $miscellaneous = (float) ($get('miscellaneous') ?? 0);
                                $overallTotal = $totalTuition + $miscellaneous;
                                $set('overall_total', $overallTotal);

                                // Recalculate balance
                                $downpayment = (float) ($get('downpayment') ?? 0);
                                $balance = $overallTotal - $downpayment;
                                $set('total_balance', $balance);
                            })
                            ->label('Miscellaneous'),
                        Forms\Components\TextInput::make('overall_total')
                            ->numeric()
                            ->live(onBlur: true)
                            ->prefix('₱')
                            ->default(fn ($record) => $record && $record->studentTuition ? $record->studentTuition->overall_tuition : null)
                            ->afterStateUpdated(function (Get $get, Set $set, $record): void {
                                // Mark as manually modified and store original value
                                $set('is_overall_manually_modified', true);
                                $set('original_overall_amount', $get('overall_total'));

                                // Recalculate balance when overall total changes
                                $overallTotal = (float) ($get('overall_total') ?? 0);
                                $downpayment = (float) ($get('downpayment') ?? 0);
                                $balance = $overallTotal - $downpayment;
                                $set('total_balance', $balance);

                                // Persist changes if in edit context
                                if ($record instanceof \App\Models\StudentEnrollment) {
                                    \App\Providers\EnrollmentServiceProvider::updateOrCreateTuitionWithManualOverride(
                                        $record,
                                        $get,
                                        $overallTotal
                                    );
                                }
                            })
                            ->helperText(function (Get $get): ?string {
                                return $get('is_overall_manually_modified')
                                    ? '⚠️ This value has been manually modified and will not auto-calculate.'
                                    : 'This value is automatically calculated. Edit to override.';
                            })
                            ->suffixAction(
                                \Filament\Forms\Components\Actions\Action::make('reset_overall_total')
                                    ->icon('heroicon-m-arrow-path')
                                    ->tooltip('Reset to auto-calculated value')
                                    ->visible(fn (Get $get): bool => $get('is_overall_manually_modified'))
                                    ->action(function (Set $set, Get $get, $record): void {
                                        // Reset the manual override flag
                                        $set('is_overall_manually_modified', false);
                                        $set('original_overall_amount', null);

                                        // Trigger recalculation
                                        \App\Providers\EnrollmentServiceProvider::updateTotals($get, $set, $record);
                                    })
                            )
                            ->label('Overall Total'),
                        Forms\Components\TextInput::make('downpayment')
                            ->numeric()
                            ->prefix('₱')
                            ->live(onBlur: true)
                            ->minValue(500)
                            ->default(3500)
                            ->afterStateUpdated(function (Get $get, Set $set): void {
                                // Recalculate balance when downpayment changes
                                $overallTotal = (float) ($get('overall_total') ?? 0);
                                $downpayment = (float) ($get('downpayment') ?? 0);
                                $balance = $overallTotal - $downpayment;
                                $set('total_balance', $balance);
                            })
                            ->label('Down Payment'),
                        Forms\Components\TextInput::make('total_balance')
                            ->numeric()
                            ->readOnly()
                            ->live()
                            ->prefix('₱')
                            ->label('Balance'),
                    ]),

                // Remarks
                Forms\Components\Textarea::make('remarks')
                    ->label('Remarks')
                    ->columnSpanFull(),
            ])
            ->columns(3);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('student_id')->badge()->searchable()->sortable(),
                Tables\Columns\TextColumn::make('student.last_name')->label('Last Name')->sortable(),
                Tables\Columns\TextColumn::make('student.first_name')->label('First Name')->sortable(),
                Tables\Columns\TextColumn::make('student.course.code')->badge()->sortable(),
                Tables\Columns\TextColumn::make('status')->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'Pending' => 'warning', 'Verified By Department Head' => 'success', 'Verified By Cashier' => 'primary', default => 'gray',
                    }),
                Tables\Columns\TextColumn::make('academic_year')->label('Year Level')->badge(),
                Tables\Columns\TextColumn::make('created_at')->label('Date Enrolled')->dateTime()->sortable()->toggleable(isToggledHiddenByDefault: false),
            ])
            ->defaultSort('created_at', 'desc')
            ->deferLoading()
            ->modifyQueryUsing(fn (Builder $query) => $query->withTrashed())
            ->searchPlaceholder('Search by student name or ID')
            ->filters([
                Tables\Filters\TrashedFilter::make()->default('with'),
                Tables\Filters\SelectFilter::make('status')->options(['Pending' => 'Pending', 'Verified By Department Head' => 'Verified By Department Head', 'Verified By Cashier' => 'Verified By Cashier']),
                Tables\Filters\SelectFilter::make('semester')->options(['1' => '1st Semester', '2' => '2nd Semester']),
                Tables\Filters\SelectFilter::make('academic_year')->options(['1' => '1st Year', '2' => '2nd Year', '3' => '3rd Year', '4' => '4th Year']),
                Tables\Filters\Filter::make('has_discount')->label('Has Discount')
                    ->query(fn (Builder $query): Builder => $query->whereRaw('EXISTS (SELECT 1 FROM student_tuition WHERE student_tuition.enrollment_id = student_enrollment.id AND student_tuition.discount > 0 AND student_tuition.deleted_at IS NULL)'))->toggle(),
                Tables\Filters\Filter::make('course')
                    ->form([Select::make('course_id')->label('Course')->options(Course::pluck('code', 'id'))->searchable()->multiple()->preload()])
                    ->query(function (Builder $query, array $data): Builder {
                        if (! isset($data['course_id']) || empty($data['course_id'])) {
                            return $query;
                        }

                        return $query->whereExists(function ($query) use ($data): void {
                            $query->select(DB::raw(1))->from('students')->whereRaw('CAST(student_enrollment.student_id AS BIGINT) = students.id')->whereIn('students.course_id', $data['course_id'])->whereNull('students.deleted_at');
                        });
                    }),
                Tables\Filters\SelectFilter::make('academic_year')->options(['1' => '1st Year', '2' => '2nd Year', '3' => '3rd Year', '4' => '4th Year']),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\ActionGroup::make([
                    Tables\Actions\Action::make('create_new_assessment_pdf')
                        ->label('Create New Assessment PDF')
                        ->icon('heroicon-o-document-plus')
                        ->color('info')
                        ->visible(fn ($record): bool => $record->status === 'Verified By Cashier')
                        ->requiresConfirmation()
                        ->modalHeading('Create New Assessment PDF')
                        ->modalDescription('This will generate a new assessment PDF for this student enrollment without overwriting the existing one. This is useful when there are issues with class schedules not showing up correctly in the PDF.')
                        ->action(function ($record): void {
                            try {
                                // Dispatch the PDF generation job with flag to create new file
                                \App\Jobs\GenerateAssessmentPdfJob::dispatch($record, null, true);

                                \Filament\Notifications\Notification::make()
                                    ->title('New PDF Generation Queued')
                                    ->body('A new assessment PDF generation has been queued and will be processed shortly.')
                                    ->success()
                                    ->send();

                                \Log::info("New assessment PDF generation queued for enrollment {$record->id}", [
                                    'enrollment_id' => $record->id,
                                    'student_id' => $record->student_id,
                                    'student_name' => $record->student->full_name ?? 'Unknown',
                                    'create_new' => true,
                                ]);

                            } catch (Exception $e) {
                                \Filament\Notifications\Notification::make()
                                    ->title('PDF Generation Failed')
                                    ->body('Failed to queue PDF recreation: '.$e->getMessage())
                                    ->danger()
                                    ->send();

                                \Log::error("Failed to queue assessment PDF recreation for enrollment {$record->id}", [
                                    'enrollment_id' => $record->id,
                                    'error' => $e->getMessage(),
                                    'trace' => $e->getTraceAsString(),
                                ]);
                            }
                        }),
                    Tables\Actions\Action::make('resend_assessment_notification')
                        ->label('Resend Assessment Notification')
                        ->icon('heroicon-o-envelope')
                        ->color('warning')
                        ->visible(fn ($record): bool => $record->status === 'Verified By Cashier' && ! empty($record->student->email))
                        ->requiresConfirmation()
                        ->modalHeading('Resend Assessment Notification')
                        ->modalDescription('This will resend the assessment notification email to the student.')
                        ->action(function ($record): void {
                            try {
                                // Dispatch the notification job
                                \App\Jobs\SendAssessmentNotificationJob::dispatch($record);

                                \Filament\Notifications\Notification::make()
                                    ->title('Notification Queued')
                                    ->body('The assessment notification has been queued and will be sent shortly.')
                                    ->success()
                                    ->send();

                                \Log::info("Assessment notification resend queued for enrollment {$record->id}", [
                                    'enrollment_id' => $record->id,
                                    'student_id' => $record->student_id,
                                    'student_email' => $record->student->email,
                                ]);

                            } catch (Exception $e) {
                                \Filament\Notifications\Notification::make()
                                    ->title('Notification Failed')
                                    ->body('Failed to queue notification: '.$e->getMessage())
                                    ->danger()
                                    ->send();

                                \Log::error("Failed to queue assessment notification for enrollment {$record->id}", [
                                    'enrollment_id' => $record->id,
                                    'error' => $e->getMessage(),
                                ]);
                            }
                        }),
                    Tables\Actions\DeleteAction::make(),
                    Tables\Actions\ForceDeleteAction::make(),
                    Tables\Actions\RestoreAction::make(),
                ])
                    ->label('More Options')
                    ->icon('heroicon-m-ellipsis-vertical')
                    ->size('sm')
                    ->color('gray'),
            ])
            ->bulkActions([
                ExportBulkAction::make('Export')->exporter(StudentEnrollmentExporter::class),
                Tables\Actions\BulkAction::make('bulk_recreate_assessment_pdfs')
                    ->label('Recreate Assessment PDFs')
                    ->icon('heroicon-o-document-duplicate')
                    ->color('info')
                    ->requiresConfirmation()
                    ->modalHeading('Recreate Assessment PDFs')
                    ->modalDescription('This will regenerate assessment PDFs for all selected verified enrollments. This is useful when there are issues with class schedules not showing up correctly in the PDFs.')
                    ->action(function (\Illuminate\Database\Eloquent\Collection $records): void {
                        $successCount = 0;
                        $errorCount = 0;
                        $skippedCount = 0;

                        foreach ($records as $record) {
                            try {
                                // Only process verified enrollments
                                if ($record->status !== 'Verified By Cashier') {
                                    $skippedCount++;

                                    continue;
                                }

                                // Dispatch the PDF generation job
                                \App\Jobs\GenerateAssessmentPdfJob::dispatch($record);
                                $successCount++;

                                \Log::info("Bulk assessment PDF recreation queued for enrollment {$record->id}", [
                                    'enrollment_id' => $record->id,
                                    'student_id' => $record->student_id,
                                    'student_name' => $record->student->full_name ?? 'Unknown',
                                ]);

                            } catch (Exception $e) {
                                $errorCount++;
                                \Log::error("Failed to queue bulk assessment PDF recreation for enrollment {$record->id}", [
                                    'enrollment_id' => $record->id,
                                    'error' => $e->getMessage(),
                                ]);
                            }
                        }

                        // Send notification with results
                        $message = "Queued PDF recreation for {$successCount} enrollment(s).";
                        if ($skippedCount > 0) {
                            $message .= " Skipped {$skippedCount} non-verified enrollment(s).";
                        }
                        if ($errorCount > 0) {
                            $message .= " Failed to queue {$errorCount} enrollment(s).";
                        }

                        \Filament\Notifications\Notification::make()
                            ->title('Bulk PDF Recreation')
                            ->body($message)
                            ->success()
                            ->send();
                    }),
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\ForceDeleteBulkAction::make(),
                    Tables\Actions\RestoreBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [];
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->currentAcademicPeriod()
            ->withoutGlobalScopes([SoftDeletingScope::class]);
    }

    public static function getTableQuery(): Builder
    {
        $query = parent::getTableQuery();
        if (request()->has('tableSearch') && request()->input('tableSearch')) {
            $search = request()->input('tableSearch');
            $query->where(function ($query) use ($search): void {
                $query->where('student_id', 'like', "%{$search}%");
                $query->orWhereRaw('EXISTS (SELECT 1 FROM students WHERE CAST(student_enrollment.student_id AS BIGINT) = students.id AND (LOWER(students.first_name) LIKE LOWER(?) OR LOWER(students.last_name) LIKE LOWER(?)) AND students.deleted_at IS NULL)', ["%{$search}%", "%{$search}%"]);
            });
        }

        return $query;
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListStudentEnrollments::route('/'),
            'create' => Pages\CreateStudentEnrollment::route('/create'),
            'view' => Pages\ViewStudentEnrollment::route('/{record}'),
            'edit' => Pages\EditStudentEnrollment::route('/{record}/edit'),
            'view-resource' => Pages\ViewResource::route('/{record}/resource/{resourceId}'),
        ];
    }

    public static function viewResource(StudentEnrollment $record, $resourceId)
    {
        $resource = $record->resources()->findOrFail($resourceId);
        Log::info('viewResource method called', ['record_id' => $record->id, 'resource_id' => $resourceId, 'file_path' => $resource->file_path, 'disk' => $resource->disk, 'file_exists' => file_exists($resource->file_path)]);
        if ($resource->disk === 'local' && file_exists($resource->file_path)) {
            return response()->file($resource->file_path);
        }
        try {
            $path = $resource->file_path;
            if (Str::startsWith($path, '/')) {
                $path = basename((string) $path);
            }

            return response()->file(Storage::disk($resource->disk)->path($path));
        } catch (Exception $e) {
            Log::error('Error serving file', ['error' => $e->getMessage(), 'disk' => $resource->disk, 'file_path' => $resource->file_path]);
            if (file_exists($resource->file_path)) {
                return response()->file($resource->file_path);
            }
            abort(404, 'File not found');
        }

        return null;
    }

    public static function getTabs(): array
    {
        $settingsService = app(GeneralSettingsService::class);
        $currentSchoolYear = $settingsService->getCurrentSchoolYearString() ?? null;
        $currentSemester = $settingsService->getCurrentSemester() ?? null;
        $baseCountQuery = StudentEnrollment::query()
            ->when($currentSchoolYear, fn ($q) => $q->where('school_year', $currentSchoolYear))
            ->when($currentSemester, fn ($q) => $q->where('semester', $currentSemester));
        $enrollmentCounts = (clone $baseCountQuery)
            ->select([
                DB::raw('COUNT(*) as total'),
                DB::raw('SUM(CASE WHEN s.course_id IN (SELECT id FROM courses WHERE code LIKE \'BSIT%\') THEN 1 ELSE 0 END) as bsit'),
                DB::raw('SUM(CASE WHEN s.course_id IN (SELECT id FROM courses WHERE code LIKE \'BSHM%\') THEN 1 ELSE 0 END) as bshm'),
                DB::raw('SUM(CASE WHEN s.course_id IN (SELECT id FROM courses WHERE code LIKE \'BSBA%\') THEN 1 ELSE 0 END) as bsba'),
                DB::raw('SUM(CASE WHEN student_enrollment.status = \'Pending\' THEN 1 ELSE 0 END) as pending'),
                DB::raw('SUM(CASE WHEN student_enrollment.status = \'Verified By Department Head\' THEN 1 ELSE 0 END) as verified_by_head'),
                DB::raw('SUM(CASE WHEN student_enrollment.status = \'Verified By Cashier\' THEN 1 ELSE 0 END) as verified_by_cashier'),
            ])
            ->join('students as s', fn ($join) => $join->on(DB::raw('CAST(student_enrollment.student_id AS BIGINT)'), '=', 's.id'))
            ->first();

        return [
            'all' => Tab::make()->label('All Enrollments')->badge($enrollmentCounts->total ?? 0)->modifyQueryUsing(fn (Builder $query) => $query->currentAcademicPeriod()),
            'bsit' => Tab::make()->label('BSIT')->badge($enrollmentCounts->bsit ?? 0)->modifyQueryUsing(fn (Builder $query) => $query->currentAcademicPeriod()->whereRaw('EXISTS (SELECT * FROM students WHERE CAST(student_enrollment.student_id AS BIGINT) = students.id AND EXISTS (SELECT * FROM courses WHERE students.course_id = courses.id AND code LIKE \'BSIT%\'))')),
            'bshm' => Tab::make()->label('BSHM')->badge($enrollmentCounts->bshm ?? 0)->modifyQueryUsing(fn (Builder $query) => $query->currentAcademicPeriod()->whereRaw('EXISTS (SELECT * FROM students WHERE CAST(student_enrollment.student_id AS BIGINT) = students.id AND EXISTS (SELECT * FROM courses WHERE students.course_id = courses.id AND code LIKE \'BSHM%\'))')),
            'bsba' => Tab::make()->label('BSBA')->badge($enrollmentCounts->bsba ?? 0)->modifyQueryUsing(fn (Builder $query) => $query->currentAcademicPeriod()->whereRaw('EXISTS (SELECT * FROM students WHERE CAST(student_enrollment.student_id AS BIGINT) = students.id AND EXISTS (SELECT * FROM courses WHERE students.course_id = courses.id AND code LIKE \'BSBA%\'))')),
            'pending' => Tab::make()->label('Pending')->badge($enrollmentCounts->pending ?? 0)->modifyQueryUsing(fn (Builder $query) => $query->currentAcademicPeriod()->where('status', 'Pending')),
            'verified_by_head' => Tab::make()->label('Verified By Head')->badge($enrollmentCounts->verified_by_head ?? 0)->modifyQueryUsing(fn (Builder $query) => $query->currentAcademicPeriod()->where('status', 'Verified By Department Head')),
            'verified_by_cashier' => Tab::make()->label('Verified By Cashier')->badge($enrollmentCounts->verified_by_cashier ?? 0)->modifyQueryUsing(fn (Builder $query) => $query->currentAcademicPeriod()->where('status', 'Verified By Cashier')),
        ];
    }

    // Get current academic period from GeneralSettingsService
    private static function getCurrentSchoolYear(): string
    {
        $service = app(GeneralSettingsService::class);

        return $service->getCurrentSchoolYearString();
    }

    private static function getCurrentSemester(): int
    {
        $service = app(GeneralSettingsService::class);

        return $service->getCurrentSemester();
    }
}
