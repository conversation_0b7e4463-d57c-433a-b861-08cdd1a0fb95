<?php

declare(strict_types=1);

namespace App\Filament\Resources;

use App\Filament\Resources\PendingEnrollmentResource\Pages;
use App\Models\Account;
use App\Models\Course;
use App\Models\PendingEnrollment;
use Carbon\Carbon;
use Exception;
use Filament\Forms\Form;
use Filament\Infolists\Components\Grid;
use Filament\Infolists\Components\Section;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Infolist;
use Filament\Notifications\Notification as FilamentNotification;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Notification;

final class PendingEnrollmentResource extends Resource
{
    protected static ?string $model = PendingEnrollment::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                //
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('full_name')
                    ->label('Name')
                    ->getStateUsing(fn ($record): string => $record->first_name.' '.($record->middle_name ? ($record->middle_name.' ') : '').$record->last_name)
                    ->icon('heroicon-o-user')
                    ->tooltip(fn ($record) => $record->email)
                    ->searchable(),
                Tables\Columns\TextColumn::make('email')
                    ->label('Email')
                    ->icon('heroicon-o-envelope')
                    ->copyable()
                    ->copyMessage('Email copied!')
                    ->searchable(),
                Tables\Columns\TextColumn::make('course_id')
                    ->label('Course')
                    ->getStateUsing(fn ($record) => optional(Course::find($record->course_id))->code ?? $record->course_id)
                    ->badge()
                    ->color('primary'),
                Tables\Columns\TextColumn::make('academic_year')
                    ->label('Academic Year')
                    ->icon('heroicon-o-academic-cap')
                    ->sortable(),
                Tables\Columns\TextColumn::make('status')
                    ->label('Status')
                    ->badge()
                    ->color(fn ($state): string => match ($state) {
                        'pending' => 'warning',
                        'approved' => 'success',
                        'rejected' => 'danger',
                        default => 'secondary',
                    })
                    ->sortable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->label('Submitted')
                    ->dateTime('M d, Y H:i')
                    ->sortable(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('status')
                    ->options([
                        'pending' => 'Pending',
                        'approved' => 'Approved',
                        'rejected' => 'Rejected',
                    ]),
                Tables\Filters\SelectFilter::make('course_id')
                    ->label('Course')
                    ->options(Course::all()->pluck('code', 'id')->toArray()),
            ])
            ->actions([
                Tables\Actions\ViewAction::make()
                    ->label('View')
                    ->icon('heroicon-o-eye'),
                Tables\Actions\EditAction::make(),
                Tables\Actions\Action::make('approve')
                    ->label('Approve')
                    ->icon('heroicon-o-check')
                    ->color('success')
                    ->visible(fn (): bool => Auth::user()?->hasRole('super_admin') || Auth::user()?->getIsDeptHeadAttribute())
                    ->requiresConfirmation()
                    ->action(fn ($record) => app(PendingEnrollmentResource::class)::approvePendingEnrollment($record)),
                Tables\Actions\Action::make('reject')
                    ->label('Reject')
                    ->icon('heroicon-o-x-mark')
                    ->color('danger')
                    ->visible(fn (): bool => Auth::user()?->hasRole('super_admin') || Auth::user()?->getIsDeptHeadAttribute())
                    ->requiresConfirmation()
                    ->action(fn ($record) => app(PendingEnrollmentResource::class)::rejectPendingEnrollment($record)),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListPendingEnrollments::route('/'),
            'create' => Pages\CreatePendingEnrollment::route('/create'),
            'edit' => Pages\EditPendingEnrollment::route('/{record}/edit'),
            'view' => Pages\ViewPendingEnrollment::route('/{record}'),
        ];
    }

    public static function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                Section::make('Applicant Overview')
                    ->schema([
                        Grid::make(2)
                            ->schema([
                                TextEntry::make('full_name')
                                    ->label('Full Name')
                                    ->state(fn ($record): string => $record->first_name.' '.($record->middle_name ? ($record->middle_name.' ') : '').$record->last_name)
                                    ->icon('heroicon-o-user')
                                    ->size('xl')
                                    ->weight('bold'),
                                TextEntry::make('email')
                                    ->icon('heroicon-o-envelope')
                                    ->copyable()
                                    ->label('Email'),
                                TextEntry::make('status')
                                    ->badge()
                                    ->color(fn ($state): string => match ($state) {
                                        'pending' => 'warning',
                                        'approved' => 'success',
                                        'rejected' => 'danger',
                                        default => 'secondary',
                                    })
                                    ->label('Status'),
                                TextEntry::make('created_at')
                                    ->label('Submitted')
                                    ->dateTime('M d, Y H:i')
                                    ->icon('heroicon-o-clock'),
                            ]),
                    ]),
                Section::make('Personal Information')
                    ->schema([
                        Grid::make(2)
                            ->schema([
                                TextEntry::make('data.birth_date')->label('Birth Date')->icon('heroicon-o-cake'),
                                TextEntry::make('data.gender')->label('Gender')->icon('heroicon-o-user-group'),
                                TextEntry::make('data.birthplace')->label('Birthplace')->icon('heroicon-o-map-pin'),
                                TextEntry::make('data.civil_status')->label('Civil Status')->icon('heroicon-o-heart'),
                                TextEntry::make('data.citizenship')->label('Citizenship')->icon('heroicon-o-flag'),
                                TextEntry::make('data.religion')->label('Religion')->icon('heroicon-o-sparkles'),
                                TextEntry::make('data.height')->label('Height')->suffix('cm')->icon('heroicon-o-arrow-trending-up'),
                                TextEntry::make('data.weight')->label('Weight')->suffix('kg')->icon('heroicon-o-arrow-trending-down'),
                                TextEntry::make('data.current_adress')->label('Current Address')->icon('heroicon-o-home-modern'),
                                TextEntry::make('data.permanent_address')->label('Permanent Address')->icon('heroicon-o-home'),
                            ]),
                    ]),
                Section::make('Academic Information')
                    ->schema([
                        Grid::make(2)
                            ->schema([
                                TextEntry::make('data.course_id')
                                    ->label('Course')
                                    ->state(fn ($record) => optional(Course::find($record->data['course_id'] ?? null))->code ?? '-')
                                    ->icon('heroicon-o-academic-cap'),
                                TextEntry::make('data.academic_year')->label('Academic Year')->icon('heroicon-o-calendar'),
                                TextEntry::make('data.lrn')->label('LRN')->icon('heroicon-o-identification'),
                                TextEntry::make('data.enrollment_google_email')->label('Enrollment Email')->icon('heroicon-o-envelope'),
                            ]),
                    ]),
                Section::make('Contacts')
                    ->schema([
                        Grid::make(2)
                            ->schema([
                                TextEntry::make('data.personal_contact')->label('Personal Contact')->icon('heroicon-o-device-phone-mobile'),
                                TextEntry::make('data.facebook_contact')->label('Facebook')->icon('heroicon-o-globe-alt'),
                                TextEntry::make('data.emergency_contact_name')->label('Emergency Contact Name')->icon('heroicon-o-user'),
                                TextEntry::make('data.emergency_contact_phone')->label('Emergency Contact Phone')->icon('heroicon-o-phone-arrow-up-right'),
                                TextEntry::make('data.emergency_contact_address')->label('Emergency Contact Address')->icon('heroicon-o-map'),
                            ]),
                    ]),
                Section::make('Family & Guardian')
                    ->schema([
                        Grid::make(2)
                            ->schema([
                                TextEntry::make('data.fathers_name')->label("Father's Name")->icon('heroicon-o-user'),
                                TextEntry::make('data.mothers_name')->label("Mother's Name")->icon('heroicon-o-user'),
                                TextEntry::make('data.guardianName')->label('Guardian Name')->icon('heroicon-o-user'),
                                TextEntry::make('data.guardianRelationship')->label('Guardian Relationship')->icon('heroicon-o-hand-thumb-up'),
                                TextEntry::make('data.guardianContact')->label('Guardian Contact')->icon('heroicon-o-phone'),
                            ]),
                    ]),
                Section::make('Education')
                    ->schema([
                        Grid::make(2)
                            ->schema([
                                TextEntry::make('data.elementary_school')->label('Elementary School')->icon('heroicon-o-building-library'),
                                TextEntry::make('data.elementary_school_address')->label('Elementary Address')->icon('heroicon-o-map'),
                                TextEntry::make('data.elementary_graduate_year')->label('Elementary Graduate Year')->icon('heroicon-o-calendar'),
                                TextEntry::make('data.junior_high_school_name')->label('Junior High School')->icon('heroicon-o-building-library'),
                                TextEntry::make('data.junior_high_school_address')->label('Junior High Address')->icon('heroicon-o-map'),
                                TextEntry::make('data.junior_high_graduation_year')->label('Junior High Graduation Year')->icon('heroicon-o-calendar'),
                                TextEntry::make('data.senior_high_name')->label('Senior High School')->icon('heroicon-o-building-library'),
                                TextEntry::make('data.senior_high_address')->label('Senior High Address')->icon('heroicon-o-map'),
                                TextEntry::make('data.senior_high_graduate_year')->label('Senior High Graduate Year')->icon('heroicon-o-calendar'),
                            ]),
                    ]),
            ]);
    }

    // Approve logic will be implemented as a static method for use in the action
    public static function approvePendingEnrollment($record): void
    {
        try {
            // 1. Generate random password
            $password = \Illuminate\Support\Str::random(10);

            // 2. Create Account as guest (not student), do not assign person_id
            $account = Account::create([
                'name' => $record->first_name.' '.$record->last_name,
                'username' => $record->first_name.' '.$record->last_name,
                'email' => $record->email,
                'password' => bcrypt($password),
                'role' => 'guest',
                'is_active' => true,
                'person_id' => null,
                'person_type' => null,
            ]);

            // 3. Update PendingEnrollment
            $record->status = 'approved';
            $record->approved_by = Auth::id();
            $record->processed_at = Carbon::now();
            $record->save();

            // 4. Send custom email with credentials (role: guest)
            Notification::route('mail', $record->email)
                ->notify(new class($account, $password) extends \Illuminate\Notifications\Notification
                {
                    public function __construct(public $account, public $password) {}

                    public function via($notifiable): array
                    {
                        return ['mail'];
                    }

                    public function toMail($notifiable)
                    {
                        return (new MailMessage)
                            ->subject('Your DCCP Student Portal Account is Ready!')
                            ->greeting('Congratulations!')
                            ->line('Your online enrollment has been approved. You can now log in to the DCCP Student Portal to continue your enrollment process.')
                            ->line('**Portal:** [portal.dccp.edu.ph](https://portal.dccp.edu.ph)')
                            ->line('**Email:** '.$this->account->email)
                            ->line('**Password:** '.$this->password)
                            ->line('Your current role is: GUEST. You will be able to complete your enrollment and become a student after further verification.')
                            ->line('Please change your password after your first login for security.')
                            ->line('If you have any questions, contact the registrar or your department head.')
                            ->salutation('Welcome to DCCP!');
                    }
                });

            FilamentNotification::make()
                ->title('Enrollment Approved')
                ->body('Account created as guest and credentials sent to '.$record->email)
                ->success()
                ->send();
        } catch (Exception $e) {
            FilamentNotification::make()
                ->title('Approval Failed')
                ->body('Error: '.$e->getMessage())
                ->danger()
                ->send();
        }
    }

    public static function rejectPendingEnrollment($record): void
    {
        try {
            $record->status = 'rejected';
            $record->approved_by = Auth::id();
            $record->processed_at = Carbon::now();
            $record->save();

            // Optional: Send rejection email
            Notification::route('mail', $record->email)
                ->notify(new class($record) extends \Illuminate\Notifications\Notification
                {
                    public function __construct(public $record) {}

                    public function via($notifiable): array
                    {
                        return ['mail'];
                    }

                    public function toMail($notifiable)
                    {
                        return (new MailMessage)
                            ->subject('DCCP Online Enrollment Update')
                            ->greeting('Hello '.($this->record->first_name ?? 'Student').',')
                            ->line('We regret to inform you that your online enrollment request was not approved at this time.')
                            ->line('For more information, please contact your department head or the registrar.')
                            ->salutation('Thank you for your interest in DCCP.');
                    }
                });

            FilamentNotification::make()
                ->title('Enrollment Rejected')
                ->body('The pending enrollment was marked as rejected and the applicant was notified.')
                ->success()
                ->send();
        } catch (Exception $e) {
            FilamentNotification::make()
                ->title('Rejection Failed')
                ->body('Error: '.$e->getMessage())
                ->danger()
                ->send();
        }
    }
}
