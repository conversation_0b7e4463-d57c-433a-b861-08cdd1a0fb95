<?php

declare(strict_types=1);

namespace App\Filament\Resources;

use App\Filament\Resources\FacultyResource\Pages;
use App\Filament\Resources\FacultyResource\RelationManagers\ClassesRelationManager;
use App\Models\Classes;
use App\Models\Faculty;
use App\Services\GeneralSettingsService;
use DB;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Actions\Action;
use Filament\Tables\Columns\ImageColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;

final class FacultyResource extends Resource
{
    protected static ?string $model = Faculty::class;

    protected static ?string $navigationIcon = 'heroicon-s-user-group';

    protected static ?string $navigationGroup = 'User Management';

    public static function form(Form $form): Form
    {
        return $form->schema([
            Section::make('Personal Information')
                ->description('Basic information about the faculty member.')
                ->schema([
                    TextInput::make('first_name')
                        ->required()
                        ->maxLength(255),
                    TextInput::make('last_name')
                        ->required()
                        ->maxLength(255),
                    TextInput::make('middle_name')
                        ->maxLength(255),
                    Select::make('gender')
                        ->options([
                            'male' => 'Male',
                            'female' => 'Female',
                            'other' => 'Other',
                        ])->placeholder('Select Gender'),
                    DatePicker::make('birth_date')
                        ->label('Date of Birth'),
                    TextInput::make('age')->numeric(),

                ])->columns(3),

            Section::make('Contact Details')
                ->schema([
                    TextInput::make('email')
                        ->email()
                        ->required()
                        ->unique(ignoreRecord: true)
                        ->maxLength(255),
                    TextInput::make('phone_number')
                        ->tel()
                        ->maxLength(255),
                    TextInput::make('address_line1')
                        ->maxLength(255),
                ])->columns(3),
            Section::make('Professional Details')
                ->schema([
                    TextInput::make('department')
                        ->maxLength(255),
                    TextInput::make('office_hours')
                        ->maxLength(255),
                    Select::make('status')
                        ->options([
                            'active' => 'Active',
                            'inactive' => 'Inactive',
                            'on_leave' => 'On Leave',
                        ])->placeholder('Select Status'),
                ])->columns(3),

            Section::make('Additional Information')
                ->schema([
                    Textarea::make('biography')
                        ->columnSpanFull(),
                    Textarea::make('education')
                        ->columnSpanFull(),
                    Textarea::make('courses_taught')
                        ->columnSpanFull(),
                ]),
            Section::make('Avatar')
                ->schema([
                    FileUpload::make('photo_url')
                        ->label('Profile Picture')
                        ->image()
                        ->columnSpanFull(),
                ]),

            Section::make('User Account')
                ->description('Faculty login credentials.')
                ->schema([
                    TextInput::make('password')
                        ->password()
                        ->dehydrateStateUsing(fn ($state) => Hash::make($state))
                        ->dehydrated(fn ($state) => filled($state))
                        ->required(fn (string $context): bool => $context === 'create')
                        ->maxLength(255)
                        ->hint(fn ($state): string => $state ? 'Password updated' : 'Leave blank to keep unchanged')
                        ->hintColor('success')
                        ->columnSpanFull(),
                    // Select::make('roles')
                    //     ->label('Roles')
                    //     ->multiple()
                    //     ->relationship('roles', 'name')
                    //     ->preload()
                    //     ->searchable(),
                ])->columns(1),

        ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                ImageColumn::make('photo_url')
                    ->label('Avatar')
                    ->circular(),
                TextColumn::make('first_name')
                    ->label('First Name')
                    ->searchable(),
                TextColumn::make('last_name')
                    ->label('Last Name')
                    ->searchable(),
                TextColumn::make('email')
                    ->searchable(),
                TextColumn::make('department')
                    ->searchable()
                    ->toggleable(),
                TextColumn::make('status')
                    ->badge()
                    ->toggleable(),
                TextColumn::make('gender')
                    ->toggleable(),
                TextColumn::make('age')
                    ->numeric()
                    ->sortable()
                    ->toggleable(),
                TextColumn::make('birth_date')
                    ->date()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                TextColumn::make('phone_number')
                    ->toggleable(isToggledHiddenByDefault: true),
                TextColumn::make('office_hours')
                    ->toggleable(isToggledHiddenByDefault: true),
                TextColumn::make('address_line1')
                    ->toggleable(isToggledHiddenByDefault: true),
                TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                TextColumn::make('roles.name')
                    ->label('Roles')
                    ->badge()
                    ->searchable(),
                TextColumn::make('current_classes_count')
                    ->label('Current Classes')
                    ->getStateUsing(function ($record) {
                        $settingsService = app(GeneralSettingsService::class);
                        $schoolYearWithSpaces = $settingsService->getCurrentSchoolYearString();
                        $schoolYearNoSpaces = str_replace(' ', '', $schoolYearWithSpaces);
                        $semester = $settingsService->getCurrentSemester();

                        return Classes::whereRaw('faculty_id::text = ?', [$record->id])
                            ->whereIn('school_year', [$schoolYearWithSpaces, $schoolYearNoSpaces])
                            ->where('semester', $semester)
                            ->count();
                    })
                    ->badge()
                    ->color('success'),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('status')
                    ->options([
                        'active' => 'Active',
                        'inactive' => 'Inactive',
                        'on_leave' => 'On Leave',
                    ]),
                Tables\Filters\SelectFilter::make('department')
                    ->options(fn () => Faculty::whereNotNull('department')
                        ->distinct()
                        ->pluck('department', 'department')
                        ->toArray()),
                Tables\Filters\Filter::make('has_current_classes')
                    ->label('Has Current Classes')
                    ->query(function (Builder $query): Builder {
                        $settingsService = app(GeneralSettingsService::class);
                        $schoolYearWithSpaces = $settingsService->getCurrentSchoolYearString();
                        $schoolYearNoSpaces = str_replace(' ', '', $schoolYearWithSpaces);
                        $semester = $settingsService->getCurrentSemester();

                        return $query->whereExists(function ($subQuery) use ($schoolYearWithSpaces, $schoolYearNoSpaces, $semester): void {
                            $subQuery->select(DB::raw(1))
                                ->from('classes')
                                ->whereRaw('classes.faculty_id::text = faculty.id::text')
                                ->whereIn('classes.school_year', [$schoolYearWithSpaces, $schoolYearNoSpaces])
                                ->where('classes.semester', $semester);
                        });
                    }),
                Tables\Filters\Filter::make('no_current_classes')
                    ->label('No Current Classes')
                    ->query(function (Builder $query): Builder {
                        $settingsService = app(GeneralSettingsService::class);
                        $schoolYearWithSpaces = $settingsService->getCurrentSchoolYearString();
                        $schoolYearNoSpaces = str_replace(' ', '', $schoolYearWithSpaces);
                        $semester = $settingsService->getCurrentSemester();

                        return $query->whereNotExists(function ($subQuery) use ($schoolYearWithSpaces, $schoolYearNoSpaces, $semester): void {
                            $subQuery->select(DB::raw(1))
                                ->from('classes')
                                ->whereRaw('classes.faculty_id::text = faculty.id::text')
                                ->whereIn('classes.school_year', [$schoolYearWithSpaces, $schoolYearNoSpaces])
                                ->where('classes.semester', $semester);
                        });
                    }),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Action::make('assignClasses')
                    ->label('Assign Classes')
                    ->icon('heroicon-o-academic-cap')
                    ->color('info')
                    ->form([
                        Select::make('class_ids')
                            ->label('Select Classes to Assign')
                            ->multiple()
                            ->searchable()
                            ->preload()
                            ->options(function () {
                                $settingsService = app(GeneralSettingsService::class);

                                return Classes::currentAcademicPeriod()
                                    ->whereNull('faculty_id')
                                    ->get()
                                    ->mapWithKeys(function ($class) {
                                        $type = $class->isShs() ? 'SHS' : 'College';
                                        $info = $class->isShs() ? $class->formatted_track_strand : $class->formatted_course_codes;
                                        $label = "{$class->subject_title} - {$class->section} ({$type})";
                                        if ($info && $info !== 'N/A') {
                                            $label .= " - {$info}";
                                        }

                                        return [$class->id => $label];
                                    });
                            })
                            ->hint('Only showing unassigned classes for current academic period'),
                    ])
                    ->action(function (array $data, Faculty $record): void {
                        if (! empty($data['class_ids'])) {
                            Classes::whereIn('id', $data['class_ids'])
                                ->update(['faculty_id' => (string) $record->id]);

                            $count = count($data['class_ids']);
                            Notification::make()
                                ->title('Classes Assigned Successfully')
                                ->body("Assigned {$count} class(es) to {$record->full_name}")
                                ->success()
                                ->send();
                        }
                    }),
                Action::make('resetPassword')
                    ->label('Reset Password')
                    ->icon('heroicon-o-key')
                    ->color('warning')
                    ->requiresConfirmation()
                    ->action(function (Faculty $record): void {
                        $newPassword = Str::random(10);
                        $record->update([
                            'password' => Hash::make($newPassword),
                        ]);

                        Notification::make()
                            ->title('Password Reset')
                            ->body("The password for {$record->first_name} has been reset to: {$newPassword}")
                            ->sendToDatabase($record); // Send to faculty, not admin
                    }),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Action::make('bulkAssignClasses')
                        ->label('Assign Classes to Selected Faculty')
                        ->icon('heroicon-o-academic-cap')
                        ->color('info')
                        ->form([
                            Select::make('class_assignments')
                                ->label('Class Assignments')
                                ->multiple()
                                ->searchable()
                                ->preload()
                                ->options(function () {
                                    $settingsService = app(GeneralSettingsService::class);

                                    return Classes::currentAcademicPeriod()
                                        ->whereNull('faculty_id')
                                        ->get()
                                        ->mapWithKeys(function ($class) {
                                            $type = $class->isShs() ? 'SHS' : 'College';
                                            $info = $class->isShs() ? $class->formatted_track_strand : $class->formatted_course_codes;
                                            $label = "{$class->subject_title} - {$class->section} ({$type})";
                                            if ($info && $info !== 'N/A') {
                                                $label .= " - {$info}";
                                            }

                                            return [$class->id => $label];
                                        });
                                })
                                ->hint('Select classes to distribute among selected faculty members'),
                        ])
                        ->action(function (array $data, $records): void {
                            if (! empty($data['class_assignments'])) {
                                $facultyMembers = $records->toArray();
                                $classIds = $data['class_assignments'];
                                $facultyCount = count($facultyMembers);

                                if ($facultyCount > 0) {
                                    // Distribute classes evenly among selected faculty
                                    foreach ($classIds as $index => $classId) {
                                        $facultyIndex = $index % $facultyCount;
                                        $facultyId = (string) $facultyMembers[$facultyIndex]['id'];

                                        Classes::where('id', $classId)
                                            ->update(['faculty_id' => $facultyId]);
                                    }

                                    $classCount = count($classIds);
                                    Notification::make()
                                        ->title('Classes Assigned Successfully')
                                        ->body("Distributed {$classCount} class(es) among {$facultyCount} faculty member(s)")
                                        ->success()
                                        ->send();
                                }
                            }
                        }),
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            ClassesRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListFaculties::route('/'),
            'create' => Pages\CreateFaculty::route('/create'),
            'edit' => Pages\EditFaculty::route('/{record}/edit'),
            'view' => Pages\ViewFaculty::route('/{record}'),
            'manage-assignments' => Pages\ManageClassAssignments::route('/manage-assignments'),
        ];
    }
}
