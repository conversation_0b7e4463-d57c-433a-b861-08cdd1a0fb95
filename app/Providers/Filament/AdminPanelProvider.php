<?php

declare(strict_types=1);

namespace App\Providers\Filament;

use Andreia\FilamentNordTheme\FilamentNordThemePlugin;
use App\Filament\Pages\AdminLogin;
use App\Filament\Pages\GeneralSetting;
use App\Filament\Resources\GradeApprovalResource;
use App\Filament\Resources\TransactionResource;
use BezhanSalleh\FilamentShield\FilamentShieldPlugin;
use <PERSON>zhanSalleh\FilamentShield\Resources\RoleResource;
use Bytexr\QueueableBulkActions\Enums\StatusEnum;
use Bytexr\QueueableBulkActions\QueueableBulkActionsPlugin;
use CharrafiMed\GlobalSearchModal\GlobalSearchModalPlugin;
use Cmsmaxinc\FilamentErrorPages\FilamentErrorPagesPlugin;
use Devonab\FilamentEasyFooter\EasyFooterPlugin;
use DiscoveryDesign\FilamentGaze\FilamentGazePlugin;
use Filafly\PhosphorIconReplacement;
use Filament\Http\Middleware\Authenticate;
use Filament\Http\Middleware\AuthenticateSession;
use Filament\Http\Middleware\DisableBladeIconComponents;
use Filament\Http\Middleware\DispatchServingFilamentEvent;
use Filament\Navigation\MenuItem;
use Filament\Pages;
use Filament\Panel;
use Filament\PanelProvider;
use Filament\Support\Colors\Color;
use Filament\View\PanelsRenderHook;
use Filament\Widgets;
use Guava\FilamentKnowledgeBase\KnowledgeBasePlugin;
use Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse;
use Illuminate\Cookie\Middleware\EncryptCookies;
use Illuminate\Foundation\Http\Middleware\VerifyCsrfToken;
use Illuminate\Routing\Middleware\SubstituteBindings;
use Illuminate\Session\Middleware\StartSession;
use Illuminate\Support\Facades\Blade;
use Illuminate\Support\HtmlString;
use Illuminate\View\Middleware\ShareErrorsFromSession;
use Joaopaulolndev\FilamentGeneralSettings\FilamentGeneralSettingsPlugin;
use Livewire\Livewire;
use Modules\Inventory\Filament\InventoryPlugin;
use Monzer\FilamentChatifyIntegration\ChatifyPlugin;
use Njxqlus\FilamentProgressbar\FilamentProgressbarPlugin;
use Rawilk\ProfileFilament\Features;
use Rawilk\ProfileFilament\ProfileFilamentPlugin;
use Rupadana\ApiService\ApiServicePlugin;
use ShuvroRoy\FilamentSpatieLaravelBackup\FilamentSpatieLaravelBackupPlugin;
use Teguh02\FilamentDbSync\FilamentDbSync;
use Vormkracht10\FilamentMails\FilamentMails;
use Vormkracht10\FilamentMails\FilamentMailsPlugin;

final class AdminPanelProvider extends PanelProvider
{
    public function panel(Panel $panel): Panel
    {
        // Render the new Livewire component for semester and school year selection
        $panel->renderHook('panels::global-search.before', fn () =>
            // Return a view instance containing the Livewire component
            view('livewire.semester-school-year-selector'));

        $panel->renderHook(
            PanelsRenderHook::AUTH_LOGIN_FORM_BEFORE,
            fn (): string => Blade::render(
                config('app.enable_faculty_panel')
                    ? '<x-filament::link href="/faculty/login" class="fi-link text-sm">Faculty Login</x-filament::link>'
                    : ''
            )
        );

        return $panel
            ->default()
            ->id('admin')
            ->path('admin')
            ->spa()
            ->brandName('DCCP administrator')
            ->favicon(asset('favicon.ico'))
            ->login(AdminLogin::class)
            ->emailVerification()
            ->passwordReset()
            ->unsavedChangesAlerts()
            // ->viteTheme('resources/css/filament/admin/theme.css')
            ->databaseNotifications()
            ->colors([
                'primary' => Color::Indigo,
            ])
            ->discoverResources(
                in: app_path('Filament/Resources'),
                for: 'App\\Filament\\Resources'
            )
            ->resources([GradeApprovalResource::class])
            ->discoverPages(
                in: app_path('Filament/Pages'),
                for: 'App\\Filament\\Pages'
            )
            ->pages([Pages\Dashboard::class])
            ->discoverWidgets(
                in: app_path('Filament/Widgets'),
                for: 'App\\Filament\\Widgets'
            )
            ->widgets([
                Widgets\AccountWidget::class,
                Widgets\FilamentInfoWidget::class,
                // // \App\Filament\Widgets\StudentStatsWidget::class,
                // \App\Filament\Widgets\EnrollmentOverviewWidget::class,
                // // \App\Filament\Widgets\CourseDistributionWidget::class,
                // // \App\Filament\Widgets\FinancialOverviewWidget::class,
                // \App\Filament\Widgets\ClassStatsWidget::class,
            ])
            ->plugins([
                FilamentSpatieLaravelBackupPlugin::make(),
                // FilamentDbSync::make(),
                QueueableBulkActionsPlugin::make()
                // ->bulkActionModel(YourBulkActionModel::class) // (optional) - Allows you to register your own model which extends \Bytexr\QueueableBulkActions\Models\BulkAction
                // ->bulkActionRecordModel(YourBulkActionRecordModel::class) // (optional) - Allows you to register your own model for records which extends \Bytexr\QueueableBulkActions\Models\BulkActionRecord
                // ->renderHook(
                    //     PanelsRenderHook::RESOURCE_PAGES_LIST_RECORDS_TABLE_BEFORE
                    // ) // (optional) - Allows you to change where notification is rendered, multiple render hooks can be passed as array [Default: PanelsRenderHook::RESOURCE_PAGES_LIST_RECORDS_TABLE_BEFORE]
                    ->pollingInterval('5s') // (optional) - Allows you to change or disable polling interval, set to null to disable. [Default: 5s]
                    ->queue('redis', 'default') // (optional) - Allows you to change which connection and queue should be used [Default: env('QUEUE_CONNECTION'), default]
                    // ->resource(YourBulkActionResource::class) // (optional) - Allows you to change which resource should be used to display historical bulk actions
                    ->colors([
                        StatusEnum::QUEUED->value => 'slate',
                        StatusEnum::IN_PROGRESS->value => 'info',
                        StatusEnum::FINISHED->value => 'success',
                        StatusEnum::FAILED->value => 'danger',
                    ]),
                ProfileFilamentPlugin::make()->features(
                    Features::defaults()->twoFactorAuthentication(
                        enabled: false,
                        authenticatorApps: false,
                        webauthn: false,
                        passkeys: false
                    )
                ),
                KnowledgeBasePlugin::make(),
                FilamentNordThemePlugin::make(),
                ChatifyPlugin::make(),
                FilamentProgressbarPlugin::make(),
                // DashStackThemePlugin::make(),
                // ->features(
                //     Features::defaults()->useSudoMode(false)
                // ),
                PhosphorIconReplacement::make()->regular(),
                EasyFooterPlugin::make()
                    ->withLoadTime()
                    ->withBorder()
                    ->withSentence(
                        new HtmlString(
                            '<img src="https://static.cdnlogo.com/logos/l/23/laravel.svg" style="margin-right:.5rem;" alt="Laravel Logo" width="20" height="20"> Laravel'
                        )
                    )
                    ->withGithub(showLogo: true, showUrl: true),
                FilamentMailsPlugin::make(),
                FilamentGazePlugin::make(),
                GlobalSearchModalPlugin::make(),
                FilamentErrorPagesPlugin::make(),
                FilamentShieldPlugin::make(),
                // FilamentNordThemePlugin::make(),
                ApiServicePlugin::make(),
                // FilamentGeneralSettingsPlugin::make(),
                InventoryPlugin::make(),
            ])
            ->routes(fn () => FilamentMails::routes())
            ->userMenuItems([
                // MenuItem::make()
                //     ->label("Transactions")
                //     ->url(fn(): string => AdminTransactionsResource::getUrl())
                //     ->icon("heroicon-o-banknotes"),
                MenuItem::make()
                    ->label('Portal Settings')
                    ->url(fn (): string => GeneralSetting::getUrl())

                    ->icon('heroicon-o-server'),
                MenuItem::make()
                    ->label('Roles')
                    ->url(fn (): string => RoleResource::getUrl())
                    ->icon('heroicon-o-shield-check'),
                MenuItem::make()
                    ->label('Transactions')
                    ->url(fn (): string => TransactionResource::getUrl())
                    ->icon('heroicon-o-banknotes'),
            ])
            ->middleware([
                EncryptCookies::class,
                AddQueuedCookiesToResponse::class,
                StartSession::class,
                AuthenticateSession::class,
                ShareErrorsFromSession::class,
                VerifyCsrfToken::class,
                SubstituteBindings::class,
                DisableBladeIconComponents::class,
                DispatchServingFilamentEvent::class,
            ])
            ->authMiddleware([Authenticate::class]);
    }
}
