<?php

declare(strict_types=1);

namespace App\Providers;

use App\Models\User;
use Guava\FilamentKnowledgeBase\Filament\Panels\KnowledgeBasePanel;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\ServiceProvider;

final class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        KnowledgeBasePanel::configureUsing(
            fn (KnowledgeBasePanel $panel): KnowledgeBasePanel => $panel
                ->viteTheme('resources/css/filament/admin/theme.css')
                ->syntaxHighlighting() // your filament vite theme path here
        );
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        Gate::define('viewApiDocs', fn (User $user): bool => $user->email === '<EMAIL>');

    }
}
