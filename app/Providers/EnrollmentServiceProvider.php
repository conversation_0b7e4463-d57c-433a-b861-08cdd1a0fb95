<?php

declare(strict_types=1);

namespace App\Providers;

use App\Models\Course;
use App\Models\GeneralSetting;
use App\Models\Student;
use App\Models\StudentEnrollment;
use App\Models\StudentTuition;
use App\Models\Subject;
use App\Models\SubjectEnrollment;
// Added missing use statement
use Exception;
use Filament\Forms\Get;
use Filament\Forms\Set;
use Illuminate\Support\HtmlString;
use Illuminate\Support\ServiceProvider;

// Assuming this exists based on CreateStudentEnrollment

final class EnrollmentServiceProvider extends ServiceProvider
{
    /**
     * Updates the tuition totals based on enrolled subjects and form state.
     * Handles both automatic calculation and respecting manual overrides.
     *
     * @param  Get  $get  Filament Get instance
     * @param  Set  $set  Filament Set instance
     * @param  StudentEnrollment|array|null  $record  The current enrollment record (null if creating)
     */
    public static function updateTotals(Get $get, Set $set, $record = null): void // Changed type hint to allow array and null
    {
        // Skip if record is not a StudentEnrollment
        if ($record !== null && ! ($record instanceof StudentEnrollment)) {
            return;
        }

        // Check if tuition was manually modified and should be preserved (only in edit context)
        if (
            $record instanceof StudentEnrollment &&
            $record->studentTuition &&
            $get('is_manually_modified')
        ) {
            $tuition = $record->studentTuition;
            // $miscellaneousFees = $record->course->miscelaneous; // Course might not be loaded, fetch if needed or pass in

            $set(
                'total_lectures',
                number_format($tuition->total_lectures, 2, '.', '')
            );
            $set(
                'total_laboratory',
                number_format($tuition->total_laboratory, 2, '.', '')
            );
            $set(
                'Total_Tuition',
                number_format($tuition->total_tuition, 2, '.', '')
            );
            $set(
                'miscellaneous',
                number_format($tuition->total_miscelaneous_fees, 2, '.', '') // Use stored misc fees
            );
            $set(
                'overall_total',
                number_format($tuition->overall_tuition, 2, '.', '')
            );
            $set(
                'total_balance',
                number_format($tuition->total_balance, 2, '.', '')
            );

            return; // Return early as we are using stored values
        }

        // Proceed with calculation if not manually modified or if creating
        $subjectsEnrolled = $get('subjectsEnrolled');
        $totalLecture = 0;
        $totalLaboratory = 0;
        $miscellaneousFee = 3500; // Default miscellaneous fee

        // Eager load course if record exists to get miscellaneous fees
        // Ensure student relationship is loaded before accessing course
        if ($record && $record->relationLoaded('student') && $record->student && $record->student->relationLoaded('course') && $record->student->course) {
            $miscellaneousFee = $record->student->course->miscelaneous ?? 3500;
        } elseif ($record && $record->student_id) {
            // If record exists but course not loaded, fetch it
            $student = Student::with('course')->find($record->student_id);
            if ($student && $student->course) {
                $miscellaneousFee = $student->course->miscelaneous ?? 3500;
            }
        } elseif ($get('student_id')) {
            // If creating and student_id is set, fetch student and course
            $student = Student::with('course')->find($get('student_id'));
            if ($student && $student->course) {
                $miscellaneousFee = $student->course->miscelaneous ?? 3500;
            }
        }

        if ($subjectsEnrolled !== null) {
            foreach ($subjectsEnrolled as $subject) {
                if (! empty($subject['subject_id'])) {
                    // If subject is marked as modular
                    if (! empty($subject['is_modular'])) {
                        $totalLecture += 2400; // Fixed modular fee

                        continue; // Skip lab fee for modular
                    }

                    // Always use the values from the form for lecture and lab fees
                    $totalLecture += (float) ($subject['lecture'] ?? 0);
                    $totalLaboratory += (float) ($subject['laboratory'] ?? 0);
                }
            }
        }

        $discount = (int) ($get('discount') ?? 0);
        $discountedLecture = $totalLecture * (1 - $discount / 100);
        $discountedTuition = $discountedLecture + $totalLaboratory;

        $overallTotal = $discountedTuition + $miscellaneousFee;
        $downPayment = (float) ($get('downpayment') ?? 0); // Ensure downpayment is float
        $balance = $overallTotal - $downPayment;

        // Set the calculated values in the form
        $set('total_lectures', number_format($discountedLecture, 2, '.', ''));
        $set('total_laboratory', number_format($totalLaboratory, 2, '.', ''));
        $set('Total_Tuition', number_format($discountedTuition, 2, '.', ''));
        $set('miscellaneous', number_format($miscellaneousFee, 2, '.', ''));
        $set('overall_total', number_format($overallTotal, 2, '.', ''));
        $set('total_balance', number_format($balance, 2, '.', ''));

        // Update or create tuition record if in edit context
        // Note: In 'create' context, tuition is typically handled in `afterCreate`.
        // This call might be redundant here if also called from afterStateUpdated/deleteAction.
        // Consider calling updateOrCreateTuition only when necessary (e.g., on form save).
        // For now, keep the logic but be aware it might need adjustment.
        if ($record instanceof StudentEnrollment) {
            self::updateOrCreateTuition( // Changed $this to self::
                $record,
                $get,
                $discountedTuition,
                $overallTotal,
                $discountedLecture,
                $totalLaboratory,
                $miscellaneousFee,
                $discount,
                $downPayment
            );
        }
    }

    /**
     * Recalculates totals specifically when manual overrides or discounts change.
     *
     * @param  Get  $get  Filament Get instance
     * @param  Set  $set  Filament Set instance
     * @param  StudentEnrollment|array|null  $record  The current enrollment record
     */
    public static function recalculateTotals(Get $get, Set $set, $record = null): void // Changed type hint
    {
        // Skip if record is not a StudentEnrollment
        if ($record !== null && ! ($record instanceof StudentEnrollment)) {
            return;
        }

        // Use the manually entered lecture/lab totals if available, otherwise calculate from original
        $originalLecture = (float) ($get('original_lecture_amount') ?: $get('total_lectures')); // Fallback to current total if original not set
        $totalLaboratory = (float) $get('total_laboratory'); // Use the current lab total from the form
        $discount = (int) ($get('discount') ?? 0);
        $miscellaneousFee = 3500; // Default

        // Fetch miscellaneous fee based on record or student_id
        // Ensure student relationship is loaded before accessing course
        if ($record && $record->relationLoaded('student') && $record->student && $record->student->relationLoaded('course') && $record->student->course) {
            $miscellaneousFee = $record->student->course->miscelaneous ?? 3500;
        } elseif ($record && $record->student_id) {
            $student = Student::with('course')->find($record->student_id);
            if ($student && $student->course) {
                $miscellaneousFee = $student->course->miscelaneous ?? 3500;
            }
        } elseif ($get('student_id')) {
            $student = Student::with('course')->find($get('student_id'));
            if ($student && $student->course) {
                $miscellaneousFee = $student->course->miscelaneous ?? 3500;
            }
        }

        // Apply discount only to the lecture portion, using the original/current lecture amount
        $discountedLectures = $originalLecture * (1 - $discount / 100);
        $discountedTuition = $discountedLectures + $totalLaboratory; // Add the current lab total

        $overallTotal = $discountedTuition + $miscellaneousFee;
        $downPayment = (float) ($get('downpayment') ?? 0); // Ensure float
        $balance = $overallTotal - $downPayment;

        // Update the form fields
        $set('total_lectures', number_format($discountedLectures, 2, '.', ''));
        $set('Total_Tuition', number_format($discountedTuition, 2, '.', ''));
        $set('miscellaneous', number_format($miscellaneousFee, 2, '.', '')); // Set misc fee
        $set('overall_total', number_format($overallTotal, 2, '.', ''));
        $set('total_balance', number_format($balance, 2, '.', ''));

        // Persist changes if in edit context
        if ($record instanceof StudentEnrollment) {
            self::updateOrCreateTuition( // Changed $this to self::
                $record,
                $get,
                $discountedTuition,
                $overallTotal,
                $discountedLectures, // Pass the newly calculated discounted lecture
                $totalLaboratory,    // Pass the current total laboratory
                $miscellaneousFee,
                $discount,
                $downPayment
            );
        }
    }

    /**
     * Generates an HTML table representing the student's subject checklist.
     *
     * @param  int|null  $academicYear  (Currently unused in the logic, but kept for signature consistency)
     */
    public static function generateChecklistTable(?int $courseId, ?int $studentId, ?int $academicYear): HtmlString // Made static
    {
        if ($courseId === null || $courseId === 0 || ($studentId === null || $studentId === 0)) {
            return new HtmlString('<p class="text-warning-600">Please select a student and course first.</p>');
        }

        try {
            $subjects = Subject::select(
                'id',
                'code',
                'title',
                'semester',
                'academic_year',
                'units' // Assuming 'units' exists, adjust if needed
            )
                ->where('course_id', $courseId)
                ->orderBy('academic_year')
                ->orderBy('semester')
                ->get()
                ->groupBy(['academic_year', 'semester']);

            $enrolledSubjects = SubjectEnrollment::where('student_id', $studentId)
                ->get()
                ->keyBy('subject_id'); // Key by subject_id for easy lookup

            $table = '<div class="space-y-4">';

            foreach ($subjects as $year => $semesters) {
                $table .=
                    '<div class="rounded-xl border border-gray-200 dark:border-gray-700 p-4">';
                $table .=
                    '<h2 class="text-xl font-bold mb-4">Academic Year '.
                    $year.
                    '</h2>';

                foreach ($semesters as $semester => $semesterSubjects) {
                    $table .= '<div class="mb-6">';
                    $table .=
                        '<h3 class="text-lg font-medium mb-2">Semester '.
                        $semester.
                        '</h3>';
                    $table .= '<div class="overflow-x-auto">';
                    $table .=
                        '<table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">';
                    $table .= '<thead class="bg-gray-50 dark:bg-gray-800">';
                    $table .= '<tr>';
                    $table .=
                        '<th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Code</th>';
                    $table .=
                        '<th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Title</th>';
                    $table .=
                        '<th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Units</th>';
                    $table .=
                        '<th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Status</th>';
                    $table .=
                        '<th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Grade</th>';
                    $table .= '</tr>';
                    $table .= '</thead>';
                    $table .=
                        '<tbody class="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">';

                    foreach ($semesterSubjects as $index => $subject) {
                        $enrolledSubject = $enrolledSubjects->get($subject->id); // Use get() for safety
                        $grade = $enrolledSubject->grade ?? '-';
                        $status = 'Not Completed';
                        $statusColor = 'bg-danger-100 text-danger-800 dark:bg-danger-900 dark:text-danger-300';

                        if ($enrolledSubject) {
                            // Determine status based on grade presence and value
                            if ($grade !== null && $grade !== '-' && is_numeric($grade) && $grade >= 75) {
                                $status = 'Completed';
                                $statusColor = 'bg-success-100 text-success-800 dark:bg-success-900 dark:text-success-300';
                            } elseif ($grade !== null && $grade !== '-' && is_numeric($grade) && $grade < 75) {
                                $status = 'Failed'; // Or another appropriate status
                                $statusColor = 'bg-danger-100 text-danger-800 dark:bg-danger-900 dark:text-danger-300';
                            } else {
                                // Grade might be null, '-', or non-numeric - consider it In Progress or Pending
                                $status = 'In Progress'; // Or Pending?
                                $statusColor = 'bg-warning-100 text-warning-800 dark:bg-warning-900 dark:text-warning-300';
                            }
                        }

                        $gradeClass = 'text-gray-800 dark:text-gray-200'; // Default class
                        if (is_numeric($grade)) {
                            $gradeValue = (float) $grade;
                            // Assuming GradeEnum exists and has fromGrade and getColor methods
                            if (class_exists(\App\Enums\GradeEnum::class)) {
                                $gradeEnum = \App\Enums\GradeEnum::fromGrade($gradeValue);
                                $gradeColor = $gradeEnum->getColor();
                                $gradeClass = match ($gradeColor) {
                                    'primary' => 'text-primary-600 dark:text-primary-400 font-bold',
                                    'info' => 'text-info-600 dark:text-info-400 font-bold',
                                    'success' => 'text-success-600 dark:text-success-400 font-bold',
                                    'warning' => 'text-warning-600 dark:text-warning-400 font-bold',
                                    'danger' => 'text-danger-600 dark:text-danger-400 font-bold',
                                    default => $gradeClass, // Keep default if color not matched
                                };
                            }
                        }

                        $rowClass =
                            $index % 2 === 0
                                ? 'bg-gray-50 dark:bg-gray-800'
                                : 'bg-white dark:bg-gray-900';

                        $table .= '<tr class="'.$rowClass.'">';
                        $table .=
                            '<td class="px-6 py-4 whitespace-nowrap text-sm">'.
                            htmlspecialchars((string) $subject->code).
                            '</td>';
                        $table .=
                            '<td class="px-6 py-4 whitespace-nowrap text-sm">'.
                            htmlspecialchars((string) $subject->title).
                            '</td>';
                        $table .=
                            '<td class="px-6 py-4 whitespace-nowrap text-sm text-right">'.
                            htmlspecialchars($subject->units ?? '-'). // Handle potential null units
                            '</td>';
                        $table .=
                            '<td class="px-6 py-4 whitespace-nowrap text-sm"><span class="'.
                            $statusColor.
                            ' px-2 py-1 rounded-md text-xs font-medium">'. // Added text-xs font-medium
                            htmlspecialchars($status).
                            '</span></td>';
                        $table .=
                            '<td class="px-6 py-4 whitespace-nowrap text-sm '.
                            $gradeClass.
                            '">'.
                            htmlspecialchars((string) $grade).
                            '</td>';
                        $table .= '</tr>';
                    }

                    $table .= '</tbody>';
                    $table .= '</table>';
                    $table .= '</div>'; // overflow-x-auto
                    $table .= '</div>'; // mb-6
                }

                $table .= '</div>'; // rounded-xl border...
            }

            $table .= '</div>'; // space-y-4

            return new HtmlString($table);

        } catch (Exception $e) {
            // Log the error for debugging
            \Illuminate\Support\Facades\Log::error('Error generating checklist table: '.$e->getMessage(), [
                'courseId' => $courseId,
                'studentId' => $studentId,
                'exception' => $e,
            ]);

            return new HtmlString('<p class="text-danger-600">Error generating checklist. Please check logs.</p>');
        }
    }

    /**
     * Register services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        //
    }

    /**
     * Updates or creates the StudentTuition record associated with an enrollment.
     * NOTE: This remains static as it's called by other static form utility methods.
     *
     * @param  StudentEnrollment  $record  The enrollment record
     * @param  Get  $get  Filament Get instance
     * @param  float  $discountedTuition  Calculated total tuition after discount
     * @param  float  $overallTotal  Calculated overall total including miscellaneous fees
     * @param  float  $discountedLecture  Calculated lecture fee after discount
     * @param  float  $totalLaboratory  Calculated total laboratory fee
     * @param  float  $miscellaneousFee  Miscellaneous fee amount
     * @param  int  $discount  Discount percentage applied
     * @param  float  $downPayment  Down payment amount
     */
    private static function updateOrCreateTuition(// Made static
        StudentEnrollment $record,
        Get $get,
        float $discountedTuition,
        float $overallTotal,
        float $discountedLecture,
        float $totalLaboratory,
        float $miscellaneousFee,
        int $discount,
        float $downPayment
    ): void {
        // Ensure the record is a valid StudentEnrollment
        if (! $record->id) {
            return;
        }

        $tuitionData = [
            'student_id' => $record->student_id ?? $get('student_id'), // Ensure student_id is set
            'total_tuition' => $discountedTuition,
            'total_balance' => $overallTotal - $downPayment,
            'total_lectures' => $discountedLecture,
            'total_laboratory' => $totalLaboratory,
            'total_miscelaneous_fees' => $miscellaneousFee,
            'discount' => $discount,
            'downpayment' => $downPayment,
            'overall_tuition' => $overallTotal,
            // Consider adding semester, school_year, academic_year if needed here
            // 'semester' => $record->semester ?? $get('semester'),
            // 'school_year' => GeneralSetting::first()?->getSchoolYearString(), // Fetch current school year
            // 'academic_year' => $record->academic_year ?? $get('academic_year'),
        ];

        // Ensure student_id is present before attempting to update/create
        if (empty($tuitionData['student_id'])) {
            // Log error or handle appropriately - cannot save tuition without student_id
            \Illuminate\Support\Facades\Log::error('Cannot update/create tuition: student_id is missing.', ['enrollment_id' => $record->id]);

            return;
        }

        // Use updateOrCreate for efficiency and atomicity
        StudentTuition::updateOrCreate(
            ['enrollment_id' => $record->id], // Find by enrollment_id
            $tuitionData // Data to update or create with
        );
    }

    // Methods checkFullClasses, createStudentTuition, verifyByHeadDept, verifyByCashier, and resendAssessmentNotification
    // have been moved to EnrollmentService or are static utility methods.
}
