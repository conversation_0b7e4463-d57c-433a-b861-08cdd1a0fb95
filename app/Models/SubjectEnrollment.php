<?php

declare(strict_types=1);

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * Class SubjectEnrollment
 *
 * @property int $id
 * @property int|null $subject_id
 * @property int|null $class_id
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 * @property float|null $grade
 * @property string|null $instructor
 * @property int|null $student_id
 * @property string|null $academic_year
 * @property string|null $school_year
 * @property int|null $semester
 * @property int|null $enrollment_id
 * @property string|null $remarks
 * @property string|null $classification
 * @property string|null $school_name
 * @property bool $is_credited
 * @property int|null $credited_subject_id
 * @property string|null $section
 * @property bool $is_modular
 * @property float|null $lecture_fee
 * @property float|null $laboratory_fee
 */
final class SubjectEnrollment extends Model
{
    protected $table = 'subject_enrollments';

    protected $casts = [
        'subject_id' => 'int',
        'class_id' => 'int',
        'grade' => 'float',
        'student_id' => 'int',
        'semester' => 'int',
        'enrollment_id' => 'int',
        'is_credited' => 'bool',
        'credited_subject_id' => 'int',
        'is_modular' => 'bool',
        'lecture_fee' => 'float',
        'laboratory_fee' => 'float',
    ];

    protected $fillable = [
        'subject_id',
        'class_id',
        'grade',
        'instructor',
        'student_id',
        'academic_year',
        'school_year',
        'semester',
        'enrollment_id',
        'remarks',
        'classification',
        'school_name',
        'is_credited',
        'credited_subject_id',
        'section',
        'is_modular',
        'lecture_fee',
        'laboratory_fee',
    ];

    public function guestEnrollment(): BelongsTo
    {
        return $this->belongsTo(GuestEnrollment::class, 'enrollment_id', 'id');
    }

    public function class(): BelongsTo
    {
        return $this->belongsTo(Classes::class, 'class_id');
    }

    public function subject(): BelongsTo
    {
        return $this->belongsTo(Subject::class, 'subject_id');
    }

    public function student(): BelongsTo
    {
        return $this->belongsTo(Student::class, 'student_id');
    }

    public function studentEnrollment(): BelongsTo
    {
        return $this->belongsTo(StudentEnrollment::class, 'enrollment_id');
    }

    protected static function boot(): void
    {
        parent::boot();

        self::creating(function ($model): void {
            $highestId = static::max('id');
            $model->id = $highestId ? $highestId + 1 : 1;
        });

    }
}
